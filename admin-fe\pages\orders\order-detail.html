<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chi tiết đơn hàng - Tina Shop Admin</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.3/dist/sweetalert2.min.css">
  <!-- Common CSS -->
  <link rel="stylesheet" href="../../assets/css/common.css">
  <!-- Layout CSS -->
  <link rel="stylesheet" href="../../layout/header/header.css">
  <link rel="stylesheet" href="../../layout/sidebar/sidebar.css">
  <link rel="stylesheet" href="../../layout/footer/footer.css">
  <!-- Page CSS -->
  <link rel="stylesheet" href="orders.css">
</head>
<body>
  <!-- Header -->
  <div id="header-container"></div>
  
  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <div id="sidebar-container"></div>
      
      <!-- Main content -->
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
          <h1 class="h2">Chi tiết đơn hàng <span id="orderIdTitle"></span></h1>
          <div class="btn-toolbar mb-2 mb-md-0">
            <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="btnPrint">
              <i class="bi bi-printer"></i> In đơn hàng
            </button>
            <a href="orders.html" class="btn btn-sm btn-outline-primary">
              <i class="bi bi-arrow-left"></i> Quay lại
            </a>
          </div>
        </div>
        
        <!-- Thông tin đơn hàng -->
        <div class="row mb-4">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Thông tin đơn hàng</h5>
              </div>
              <div class="card-body">
                <table class="table table-borderless">
                  <tbody>
                    <tr>
                      <th width="150">Mã đơn hàng:</th>
                      <td id="orderId"></td>
                    </tr>
                    <tr>
                      <th>Ngày đặt:</th>
                      <td id="orderDate"></td>
                    </tr>
                    <tr>
                      <th>Trạng thái:</th>
                      <td id="orderStatus"></td>
                    </tr>
                    <tr>
                      <th>Thanh toán:</th>
                      <td id="paymentMethod"></td>
                    </tr>
                    <tr>
                      <th>Trạng thái thanh toán:</th>
                      <td id="paymentStatus"></td>
                    </tr>
                    <tr>
                      <th>Ghi chú:</th>
                      <td id="orderNote"></td>
                    </tr>
                  </tbody>
                </table>
                <button class="btn btn-primary" id="btnUpdateStatus">Cập nhật trạng thái</button>
              </div>
            </div>
          </div>
          
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Thông tin khách hàng</h5>
              </div>
              <div class="card-body">
                <table class="table table-borderless">
                  <tbody>
                    <tr>
                      <th width="150">Họ tên:</th>
                      <td id="customerName"></td>
                    </tr>
                    <tr>
                      <th>Email:</th>
                      <td id="customerEmail"></td>
                    </tr>
                    <tr>
                      <th>Điện thoại:</th>
                      <td id="customerPhone"></td>
                    </tr>
                    <tr>
                      <th>Địa chỉ giao hàng:</th>
                      <td id="shippingAddress"></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Chi tiết đơn hàng -->
        <div class="row mb-4">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Chi tiết đơn hàng</h5>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped">
                    <thead>
                      <tr>
                        <th>Sản phẩm</th>
                        <th>Biến thể</th>
                        <th>Giá</th>
                        <th>Số lượng</th>
                        <th class="text-end">Thành tiền</th>
                      </tr>
                    </thead>
                    <tbody id="orderItems">
                      <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                    </tbody>
                    <tfoot>
                      <tr>
                        <td colspan="4" class="text-end"><strong>Tổng tiền sản phẩm:</strong></td>
                        <td class="text-end" id="subtotal"></td>
                      </tr>
                      <tr>
                        <td colspan="4" class="text-end"><strong>Giảm giá:</strong></td>
                        <td class="text-end" id="discount"></td>
                      </tr>
                      <tr>
                        <td colspan="4" class="text-end"><strong>Tổng thanh toán:</strong></td>
                        <td class="text-end" id="total"></td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Lịch sử đơn hàng -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Lịch sử đơn hàng</h5>
              </div>
              <div class="card-body">
                <ul class="timeline" id="orderHistory">
                  <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                </ul>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
  
  <!-- Footer -->
  <div id="footer-container"></div>
  
  <!-- Modal cập nhật trạng thái đơn hàng -->
  <div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="updateStatusModalLabel">Cập nhật trạng thái đơn hàng</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="updateStatusForm">
            <div class="mb-3">
              <label for="orderStatusSelect" class="form-label">Trạng thái</label>
              <select class="form-select" id="orderStatusSelect" required>
                <option value="PENDING">Chờ xử lý</option>
                <option value="PROCESSING">Đang xử lý</option>
                <option value="SHIPPED">Đang giao</option>
                <option value="DELIVERED">Đã giao</option>
                <option value="CANCELLED">Đã hủy</option>
              </select>
            </div>
            
            <div class="mb-3">
              <label for="statusNote" class="form-label">Ghi chú</label>
              <textarea class="form-control" id="statusNote" rows="3"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="button" class="btn btn-primary" id="btnSaveStatus">Cập nhật</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <!-- Axios -->
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.3/dist/sweetalert2.all.min.js"></script>
  <!-- Common JS -->
  <script src="../../assets/js/common.js"></script>
  <!-- Layout JS -->
  <script src="../../layout/header/header.js"></script>
  <script src="../../layout/sidebar/sidebar.js"></script>
  <!-- Page JS -->
  <script src="order-detail.js"></script>
</body>
</html>
