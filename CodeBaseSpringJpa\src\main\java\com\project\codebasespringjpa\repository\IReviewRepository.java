package com.project.codebasespringjpa.repository;

import com.project.codebasespringjpa.entity.ProductEntity;
import com.project.codebasespringjpa.entity.ReviewEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IReviewRepository extends JpaRepository<ReviewEntity, Long> {
    List<ReviewEntity> findByProduct(ProductEntity product);
    List<ReviewEntity> findByUser(UserEntity user);
    List<ReviewEntity> findByStatus(Boolean status);
    boolean existsByProductAndUser(ProductEntity product, UserEntity user);

    @Query("SELECT AVG(r.rating) FROM ReviewEntity r WHERE r.product.id = ?1")
    Double getAverageRatingByProduct(Long productId);
}
