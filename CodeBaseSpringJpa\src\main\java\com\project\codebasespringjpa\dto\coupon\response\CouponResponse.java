package com.project.codebasespringjpa.dto.coupon.response;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CouponResponse {
    Long id;
    String code;
    String discountType;
    BigDecimal discountValue;
    BigDecimal minOrderAmount;
    LocalDateTime startDate;
    LocalDateTime endDate;
    Integer usageLimit;
    Integer usageCount;
    Boolean status;
    Boolean isActive;
    Boolean isExpired;
    LocalDateTime createDate;
    String createBy;
    LocalDateTime updateDate;
    String updateBy;
}
