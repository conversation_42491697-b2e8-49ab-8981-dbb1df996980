package com.project.codebasespringjpa.service.impl;

import com.project.codebasespringjpa.dto.wishlist.request.WishlistCreateRequest;
import com.project.codebasespringjpa.dto.wishlist.response.WishlistResponse;
import com.project.codebasespringjpa.entity.ProductEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.entity.WishlistEntity;
import com.project.codebasespringjpa.exception.AppException;
import com.project.codebasespringjpa.exception.ErrorCode;
import com.project.codebasespringjpa.mapper.WishlistMapper;
import com.project.codebasespringjpa.repository.IProductRepository;
import com.project.codebasespringjpa.repository.IUserRepository;
import com.project.codebasespringjpa.repository.IWishlistRepository;
import com.project.codebasespringjpa.service.interfaces.IWishlistService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class WishlistServiceImpl implements IWishlistService {
    IWishlistRepository wishlistRepository;
    IUserRepository userRepository;
    IProductRepository productRepository;
    WishlistMapper wishlistMapper;

    @Override
    public List<WishlistResponse> findAll() {
        List<WishlistEntity> wishlists = wishlistRepository.findAll();
        return wishlistMapper.toResponseList(wishlists);
    }

    @Override
    public WishlistResponse findById(Long id) {
        WishlistEntity wishlist = wishlistRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.WISHLIST_NOT_FOUND));
        return wishlistMapper.toResponse(wishlist);
    }

    @Override
    public List<WishlistResponse> findByUserId(Long userId) {
        UserEntity user = userRepository.findById(userId)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));
        
        List<WishlistEntity> wishlists = wishlistRepository.findByUser(user);
        return wishlistMapper.toResponseList(wishlists);
    }

    @Override
    public WishlistResponse create(WishlistCreateRequest request) {
        UserEntity user = userRepository.findById(request.getUserId())
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));
        
        ProductEntity product = productRepository.findById(request.getProductId())
                .orElseThrow(() -> new AppException(ErrorCode.PRODUCT_NOT_FOUND));
        
        // Kiểm tra xem sản phẩm đã có trong danh sách yêu thích chưa
        if (existsByUserIdAndProductId(user.getId(), product.getId())) {
            throw new AppException(ErrorCode.WISHLIST_ALREADY_EXISTS);
        }
        
        WishlistEntity wishlist = wishlistMapper.toEntity(request, user, product);
        WishlistEntity savedWishlist = wishlistRepository.save(wishlist);
        
        return wishlistMapper.toResponse(savedWishlist);
    }

    @Override
    public void delete(Long id) {
        WishlistEntity wishlist = wishlistRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.WISHLIST_NOT_FOUND));
        
        wishlistRepository.delete(wishlist);
    }

    @Override
    public boolean existsByUserIdAndProductId(Long userId, Long productId) {
        return wishlistRepository.existsByUserIdAndProductId(userId, productId);
    }
}
