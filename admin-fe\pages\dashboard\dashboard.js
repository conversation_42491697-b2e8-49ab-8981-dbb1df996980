document.addEventListener('DOMContentLoaded', function() {
  // <PERSON><PERSON>m tra đăng nhập
  if (!checkAuth()) {
    return;
  }

  // Load layout components
  loadLayoutComponents();

  // Fetch dashboard data
  fetchDashboardData();

  // Initialize charts
  initCharts();
});

// Load layout components
function loadLayoutComponents() {
  // Load header
  fetch('../../layout/header/header.html')
    .then(response => response.text())
    .then(html => {
      document.getElementById('header-container').innerHTML = html;

      // Load header script after HTML is inserted
      const headerScript = document.createElement('script');
      headerScript.src = '../../layout/header/header.js';
      document.body.appendChild(headerScript);
    });

  // Load sidebar
  fetch('../../layout/sidebar/sidebar.html')
    .then(response => response.text())
    .then(html => {
      document.getElementById('sidebar-container').innerHTML = html;

      // Load sidebar script after <PERSON><PERSON><PERSON> is inserted
      const sidebarScript = document.createElement('script');
      sidebarScript.src = '../../layout/sidebar/sidebar.js';
      document.body.appendChild(sidebarScript);
    });

  // Load footer
  fetch('../../layout/footer/footer.html')
    .then(response => response.text())
    .then(html => {
      document.getElementById('footer-container').innerHTML = html;
    });
}

// Fetch dashboard data
function fetchDashboardData() {
  const token = localStorage.getItem('token');

  // Thiết lập header cho Axios
  axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

  // Sử dụng Promise.all để gọi tất cả API cùng lúc
  Promise.allSettled([
    // Fetch tổng số đơn hàng
    axios.get('http://localhost:8080/api/orders'),

    // Fetch tổng số sản phẩm
    axios.get('http://localhost:8080/api/products/all'),

    // Fetch tổng số khách hàng
    axios.get('http://localhost:8080/api/users'),

    // Fetch báo cáo doanh thu
    axios.get('http://localhost:8080/api/reports/sales?period=month')
  ])
  .then(results => {
    // Xử lý kết quả đơn hàng
    if (results[0].status === 'fulfilled') {
      const orders = results[0].value.data;
      document.getElementById('totalOrders').textContent = orders.length;

      // Hiển thị đơn hàng gần đây
      displayRecentOrders(orders.slice(0, 5));

      // Tính tổng doanh thu từ đơn hàng
      const totalRevenue = orders.reduce((sum, order) => {
        return sum + (order.totalAmount || 0);
      }, 0);

      document.getElementById('totalRevenue').textContent = formatCurrency(totalRevenue);
    } else {
      console.error('Lỗi khi lấy dữ liệu đơn hàng:', results[0].reason);
      document.getElementById('totalOrders').textContent = '0';
      document.getElementById('totalRevenue').textContent = formatCurrency(0);
      document.getElementById('recentOrdersTable').innerHTML = '<tr><td colspan="6" class="text-center">Không có dữ liệu</td></tr>';
    }

    // Xử lý kết quả sản phẩm
    if (results[1].status === 'fulfilled') {
      const products = results[1].value.data;
      document.getElementById('totalProducts').textContent = products.length;

      // Hiển thị sản phẩm bán chạy
      displayTopProducts(products.slice(0, 5));
    } else {
      console.error('Lỗi khi lấy dữ liệu sản phẩm:', results[1].reason);
      document.getElementById('totalProducts').textContent = '0';
      document.getElementById('topProductsTable').innerHTML = '<tr><td colspan="4" class="text-center">Không có dữ liệu</td></tr>';
    }

    // Xử lý kết quả khách hàng
    if (results[2].status === 'fulfilled') {
      const users = results[2].value.data;
      document.getElementById('totalCustomers').textContent = users.totalElements || users.length || 0;

      // Hiển thị khách hàng mới
      const userList = users.content || users;
      displayNewCustomers(userList.slice(0, 5));
    } else {
      console.error('Lỗi khi lấy dữ liệu khách hàng:', results[2].reason);
      document.getElementById('totalCustomers').textContent = '0';
      document.getElementById('newCustomersTable').innerHTML = '<tr><td colspan="4" class="text-center">Không có dữ liệu</td></tr>';
    }

    // Xử lý kết quả báo cáo doanh thu
    if (results[3].status === 'fulfilled') {
      const reportData = results[3].value.data;
      if (reportData && reportData.overview) {
        document.getElementById('totalRevenue').textContent = formatCurrency(reportData.overview.totalRevenue);
      }
    }
  })
  .catch(error => {
    console.error('Lỗi khi lấy dữ liệu dashboard:', error);
    handleApiError(error);
  });
}

// Hiển thị đơn hàng gần đây
function displayRecentOrders(orders) {
  const tableBody = document.getElementById('recentOrdersTable');
  tableBody.innerHTML = '';

  if (orders.length === 0) {
    const row = document.createElement('tr');
    row.innerHTML = '<td colspan="6" class="text-center">Không có đơn hàng nào</td>';
    tableBody.appendChild(row);
    return;
  }

  orders.forEach(order => {
    const row = document.createElement('tr');

    // Tạo badge cho trạng thái
    let statusBadge = '';
    switch (order.status) {
      case 'PENDING':
        statusBadge = '<span class="badge badge-pending">Chờ xử lý</span>';
        break;
      case 'PROCESSING':
        statusBadge = '<span class="badge badge-processing">Đang xử lý</span>';
        break;
      case 'SHIPPED':
        statusBadge = '<span class="badge badge-shipped">Đang giao</span>';
        break;
      case 'DELIVERED':
        statusBadge = '<span class="badge badge-delivered">Đã giao</span>';
        break;
      case 'CANCELLED':
        statusBadge = '<span class="badge badge-cancelled">Đã hủy</span>';
        break;
      default:
        statusBadge = '<span class="badge bg-secondary">Không xác định</span>';
    }

    row.innerHTML = `
      <td>#${order.id}</td>
      <td>${order.user ? order.user.fullname : 'N/A'}</td>
      <td>${formatDate(order.orderDate)}</td>
      <td>${formatCurrency(order.totalAmount)}</td>
      <td>${statusBadge}</td>
      <td>
        <a href="../orders/order-detail.html?id=${order.id}" class="btn btn-sm btn-info">
          <i class="bi bi-eye"></i>
        </a>
      </td>
    `;

    tableBody.appendChild(row);
  });
}

// Hiển thị sản phẩm bán chạy
function displayTopProducts(products) {
  const tableBody = document.getElementById('topProductsTable');
  tableBody.innerHTML = '';

  if (products.length === 0) {
    const row = document.createElement('tr');
    row.innerHTML = '<td colspan="4" class="text-center">Không có sản phẩm nào</td>';
    tableBody.appendChild(row);
    return;
  }

  // Sắp xếp sản phẩm theo số lượng đã bán (giả định)
  products.sort((a, b) => (b.soldCount || 0) - (a.soldCount || 0));

  products.slice(0, 5).forEach(product => {
    const row = document.createElement('tr');

    row.innerHTML = `
      <td>
        <div class="d-flex align-items-center">
          <img src="${product.thumbnail || '../../assets/images/default-image.avif'}" alt="${product.name}" class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: cover;">
          <span>${product.name}</span>
        </div>
      </td>
      <td>${product.category ? product.category.name : 'N/A'}</td>
      <td>${formatCurrency(product.price)}</td>
      <td>${product.soldCount || 0}</td>
    `;

    tableBody.appendChild(row);
  });
}

// Hiển thị khách hàng mới
function displayNewCustomers(customers) {
  const tableBody = document.getElementById('newCustomersTable');
  tableBody.innerHTML = '';

  if (customers.length === 0) {
    const row = document.createElement('tr');
    row.innerHTML = '<td colspan="4" class="text-center">Không có khách hàng nào</td>';
    tableBody.appendChild(row);
    return;
  }

  // Sắp xếp khách hàng theo ngày tạo (giả định)
  customers.sort((a, b) => new Date(b.createDate) - new Date(a.createDate));

  customers.slice(0, 5).forEach(customer => {
    const row = document.createElement('tr');

    row.innerHTML = `
      <td>
        <div class="d-flex align-items-center">
          <img src="${customer.avatar || '../../assets/images/default-image.avif'}" alt="${customer.fullname}" class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
          <span>${customer.fullname}</span>
        </div>
      </td>
      <td>${customer.email}</td>
      <td>${formatDate(customer.createDate)}</td>
      <td>${customer.orderCount || 0}</td>
    `;

    tableBody.appendChild(row);
  });
}

// Khởi tạo biểu đồ
function initCharts() {
  // Biểu đồ doanh thu theo tháng
  const revenueChartCtx = document.getElementById('revenueChart').getContext('2d');

  try {
    // Tạo biểu đồ với dữ liệu mẫu trước
    const revenueChart = new Chart(revenueChartCtx, {
      type: 'line',
      data: {
        labels: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
        datasets: [{
          label: 'Doanh thu (VNĐ)',
          data: [12, 19, 15, 17, 22, 25, 32, 30, 35, 40, 45, 50],
          borderColor: '#007bff',
          backgroundColor: 'rgba(0, 123, 255, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return formatCurrency(value);
              }
            }
          }
        },
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                return formatCurrency(context.raw);
              }
            }
          }
        }
      }
    });

    // Sau đó cố gắng lấy dữ liệu từ API
    axios.get('http://localhost:8080/api/reports/sales?period=year')
      .then(response => {
        const reportData = response.data;
        const revenueByDate = reportData.revenueByDate || [];

        // Nếu có dữ liệu, cập nhật biểu đồ
        if (revenueByDate.length > 0) {
          const labels = revenueByDate.map(item => item.date);
          const data = revenueByDate.map(item => item.revenue);

          revenueChart.data.labels = labels;
          revenueChart.data.datasets[0].data = data;
          revenueChart.update();
        }
      })
      .catch(error => {
        console.error('Lỗi khi lấy dữ liệu doanh thu:', error);
        // Giữ nguyên biểu đồ mẫu nếu có lỗi
      });
  } catch (error) {
    console.error('Lỗi khi khởi tạo biểu đồ doanh thu:', error);

    // Hiển thị thông báo lỗi trong container biểu đồ
    const revenueChartContainer = document.getElementById('revenueChart').parentNode;
    revenueChartContainer.innerHTML = `
      <div class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        Không thể tải biểu đồ doanh thu. Vui lòng tải lại trang.
      </div>
    `;
  }

  // Biểu đồ đơn hàng theo trạng thái
  const orderStatusChartCtx = document.getElementById('orderStatusChart').getContext('2d');

  try {
    const orderStatusChart = new Chart(orderStatusChartCtx, {
      type: 'doughnut',
      data: {
        labels: ['Chờ xử lý', 'Đang xử lý', 'Đang giao', 'Đã giao', 'Đã hủy'],
        datasets: [{
          data: [15, 20, 10, 45, 10],
          backgroundColor: [
            '#ffc107', // Chờ xử lý
            '#17a2b8', // Đang xử lý
            '#6f42c1', // Đang giao
            '#28a745', // Đã giao
            '#dc3545'  // Đã hủy
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });

    // Có thể thêm code để lấy dữ liệu thực từ API ở đây
  } catch (error) {
    console.error('Lỗi khi khởi tạo biểu đồ trạng thái đơn hàng:', error);

    // Hiển thị thông báo lỗi trong container biểu đồ
    const orderStatusChartContainer = document.getElementById('orderStatusChart').parentNode;
    orderStatusChartContainer.innerHTML = `
      <div class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        Không thể tải biểu đồ trạng thái đơn hàng. Vui lòng tải lại trang.
      </div>
    `;
  }
}
