// API Configuration for Client

const API_BASE_URL = 'http://localhost:8080';

// Authentication token handling
function getAuthToken() {
    return localStorage.getItem('token');
}

function setAuthToken(token) {
    localStorage.setItem('token', token);
}

function removeAuthToken() {
    localStorage.removeItem('token');
}

// Common headers with authentication
function getHeaders() {
    const headers = {
        'Content-Type': 'application/json'
    };
    
    const token = getAuthToken();
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    
    return headers;
}

// Generic API request function
async function apiRequest(endpoint, method = 'GET', data = null) {
    try {
        // Ensure endpoint starts with proper path
        let url;
        if (endpoint.startsWith('auth/')) {
            url = `${API_BASE_URL}/${endpoint}`;
        } else {
            url = `${API_BASE_URL}/${endpoint}`;
        }

        const options = {
            method,
            headers: getHeaders()
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(url, options);

        // Handle 401 Unauthorized (token expired or invalid)
        if (response.status === 401 && getAuthToken()) {
            removeAuthToken();
            // Only redirect if we're not already on the login page
            if (!window.location.href.includes('/login/')) {
                window.location.href = '../../pages/login/index.html';
            }
            throw new Error('Unauthorized. Please login again.');
        }

        // Parse response
        const responseData = await response.json();

        // Check if response indicates an error
        if (!response.ok) {
            throw new Error(responseData.message || 'API request failed');
        }

        // Handle ApiResponse wrapper from backend
        if (responseData.data !== undefined) {
            return responseData.data;
        }

        return responseData;
    } catch (error) {
        console.error('API request error:', error);
        throw error;
    }
}
