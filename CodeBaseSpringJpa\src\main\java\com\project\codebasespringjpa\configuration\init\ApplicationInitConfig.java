package com.project.codebasespringjpa.configuration.init;

import com.project.codebasespringjpa.entity.*;
import com.project.codebasespringjpa.enums.*;
import com.project.codebasespringjpa.repository.*;
import com.project.codebasespringjpa.service.interfaces.IRoleService;
import com.project.codebasespringjpa.service.interfaces.IUserService;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ApplicationInitConfig {
    @Autowired
    IRoleService roleService;

    @Autowired
    IUserRepository userRepository;

    @Autowired
    ICategoryRepository categoryRepository;

    @Autowired
    IProductRepository productRepository;

    @Autowired
    ISupplierRepository supplierRepository;

    @Autowired
    IBlogRepository blogRepository;

    @Autowired
    PasswordEncoder passwordEncoder;

    @Bean
    ApplicationRunner applicationRunner(){
        return args ->{
            this.createRoles();
            this.createAccount();
            this.createCategories();
            this.createSuppliers();
            this.createProducts();
            this.createBlogs();
        };
    }

    void createRoles(){
        List<String> roleList = RoleEnum.roleList();
        for(String roleName: roleList){
            if(roleService.exitsByName(roleName) == false){
                roleService.create(roleName);
            }
        }
    }

    void createAccount(){
        if (userRepository.count() == 0){
            RoleEntity roleAdmin = new RoleEntity(RoleEnum.ADMIN.name());
            UserEntity userAdmin = new UserEntity("<EMAIL>", "admin", passwordEncoder.encode("1234"), roleAdmin);
            userAdmin.setUsername("admin");

            RoleEntity roleEmpl = new RoleEntity(RoleEnum.EMPLOYEE.name());
            UserEntity userEmployee = new UserEntity("<EMAIL>", "employee", passwordEncoder.encode("1234"), roleEmpl);
            userEmployee.setUsername("employee");

            RoleEntity roleKeeper = new RoleEntity(RoleEnum.STOREKEEPER.name());
            UserEntity userStoreKeeper = new UserEntity("<EMAIL>", "keeper", passwordEncoder.encode("1234"), roleKeeper);
            userStoreKeeper.setUsername("keeper");

            RoleEntity roleCustomer = new RoleEntity(RoleEnum.CUSTOMER.name());
            UserEntity userCustomer = new UserEntity("<EMAIL>", "customer", passwordEncoder.encode("1234"), roleCustomer);
            userCustomer.setUsername("customer");

            RoleEntity roleVisitor = new RoleEntity(RoleEnum.VISITOR.name());
            UserEntity userVisitor = new UserEntity("<EMAIL>", "visitor", passwordEncoder.encode("1234"), roleVisitor);
            userVisitor.setUsername("visitor");

            userRepository.save(userAdmin);
            userRepository.save(userEmployee);
            userRepository.save(userStoreKeeper);
            userRepository.save(userCustomer);
            userRepository.save(userVisitor);
        }
    }

    void createCategories() {
        if (categoryRepository.count() == 0) {
            // Tạo danh mục cha
            CategoryEntity necklaces = CategoryEntity.builder()
                    .name("Dây chuyền")
                    .description("Các loại dây chuyền đẹp, sang trọng")
                    .image("category_necklaces.jpg")
                    .build();

            CategoryEntity rings = CategoryEntity.builder()
                    .name("Nhẫn")
                    .description("Các loại nhẫn đẹp, sang trọng")
                    .image("category_rings.webp")
                    .build();

            CategoryEntity earrings = CategoryEntity.builder()
                    .name("Bông tai")
                    .description("Các loại bông tai đẹp, sang trọng")
                    .image("category_earrings.jpg")
                    .build();

            CategoryEntity bracelets = CategoryEntity.builder()
                    .name("Vòng tay")
                    .description("Các loại vòng tay đẹp, sang trọng")
                    .image("category_bracelets.webp")
                    .build();

            categoryRepository.saveAll(Arrays.asList(necklaces, rings, earrings, bracelets));

            // Tạo danh mục con
            CategoryEntity goldNecklaces = CategoryEntity.builder()
                    .name("Dây chuyền vàng")
                    .description("Các loại dây chuyền vàng đẹp, sang trọng")
                    .image("category_gold-necklaces.jpg")
                    .build();

            CategoryEntity silverNecklaces = CategoryEntity.builder()
                    .name("Dây chuyền bạc")
                    .description("Các loại dây chuyền bạc đẹp, sang trọng")
                    .image("category_silver-necklaces.jpg")
                    .build();

            CategoryEntity goldRings = CategoryEntity.builder()
                    .name("Nhẫn vàng")
                    .description("Các loại nhẫn vàng đẹp, sang trọng")
                    .image("category_gold-rings.jpg")
                    .build();

            CategoryEntity silverRings = CategoryEntity.builder()
                    .name("Nhẫn bạc")
                    .description("Các loại nhẫn bạc đẹp, sang trọng")
                    .image("category_silver-rings.jpg")
                    .build();

            categoryRepository.saveAll(Arrays.asList(goldNecklaces, silverNecklaces, goldRings, silverRings));
        }
    }

    void createSuppliers() {
        if (supplierRepository.count() == 0) {
            SupplierEntity supplier1 = SupplierEntity.builder()
                    .name("PNJ")
                    .email("<EMAIL>")
                    .phone("0123456789")
                    .address("170E Phan Đăng Lưu, P.3, Q.Phú Nhuận, TP.HCM")
                    .description("Công ty Cổ phần Vàng bạc đá quý Phú Nhuận (PNJ)")
                    .status(true)
                    .build();

            SupplierEntity supplier2 = SupplierEntity.builder()
                    .name("DOJI")
                    .email("<EMAIL>")
                    .phone("0987654321")
                    .address("Tầng 5, Tòa nhà DOJI Tower, Số 5 Lê Duẩn, Hà Nội")
                    .description("Tập đoàn Vàng bạc đá quý DOJI")
                    .status(true)
                    .build();

            SupplierEntity supplier3 = SupplierEntity.builder()
                    .name("SJC")
                    .email("<EMAIL>")
                    .phone("0123456788")
                    .address("418-420 Nguyễn Thị Minh Khai, P.5, Q.3, TP.HCM")
                    .description("Công ty Vàng bạc đá quý Sài Gòn - SJC")
                    .status(true)
                    .build();

            supplierRepository.saveAll(Arrays.asList(supplier1, supplier2, supplier3));
        }
    }

    void createProducts() {
        if (productRepository.count() == 0) {
            // Lấy danh mục và nhà cung cấp
            List<CategoryEntity> categories = categoryRepository.findAll();
            List<SupplierEntity> suppliers = supplierRepository.findAll();

            if (!categories.isEmpty() && !suppliers.isEmpty()) {
                CategoryEntity necklaceCategory = categories.stream()
                        .filter(c -> c.getName().contains("Dây chuyền"))
                        .findFirst()
                        .orElse(categories.get(0));

                CategoryEntity ringCategory = categories.stream()
                        .filter(c -> c.getName().contains("Nhẫn"))
                        .findFirst()
                        .orElse(categories.get(0));

                SupplierEntity pnjSupplier = suppliers.stream()
                        .filter(s -> s.getName().equals("PNJ"))
                        .findFirst()
                        .orElse(suppliers.get(0));

                SupplierEntity dojiSupplier = suppliers.stream()
                        .filter(s -> s.getName().equals("DOJI"))
                        .findFirst()
                        .orElse(suppliers.get(0));

                // Tạo sản phẩm
                ProductEntity product1 = ProductEntity.builder()
                        .name("Dây chuyền vàng 24K")
                        .description("Dây chuyền vàng 24K với thiết kế sang trọng, tinh tế")
                        .price(15000000.0)
                        .image("product_vong_vang.jpg")
                        .category(necklaceCategory)
                        .supplier(pnjSupplier)
                        .quantity(10)
                        .build();

                ProductEntity product2 = ProductEntity.builder()
                        .name("Nhẫn kim cương")
                        .description("Nhẫn kim cương cao cấp, phù hợp làm quà tặng hoặc đính hôn")
                        .price(25000000.0)
                        .image("product_nhan_kim_cuong.jpg")
                        .category(ringCategory)
                        .supplier(dojiSupplier)
                        .quantity(5)
                        .build();

                productRepository.saveAll(Arrays.asList(product1, product2));

            }
        }
    }

    void createBlogs() {
        if (blogRepository.count() == 0) {
            UserEntity admin = userRepository.findByUsername("admin").orElse(null);

            if (admin != null) {
                BlogEntity blog1 = BlogEntity.builder()
                        .title("Cách chọn trang sức phù hợp với từng dáng người")
                        .content("<p>Trang sức không chỉ là món đồ làm đẹp mà còn là cách để thể hiện cá tính và phong cách của mỗi người. Tuy nhiên, không phải món trang sức nào cũng phù hợp với mọi dáng người. Bài viết này sẽ giúp bạn hiểu rõ hơn về cách chọn trang sức phù hợp với từng dáng người.</p><p>Đối với người có dáng cao gầy, nên chọn những món trang sức có kích thước vừa phải, không quá nhỏ để tránh làm mất cân đối. Ngược lại, người có dáng thấp nên chọn những món trang sức nhỏ nhắn, tinh tế để tạo cảm giác cao hơn.</p><p>Đối với người có khuôn mặt tròn, nên chọn những đôi bông tai dài để tạo cảm giác khuôn mặt thon gọn hơn. Người có khuôn mặt dài nên chọn những đôi bông tai ngắn, tròn để cân đối khuôn mặt.</p>")
                        .build();

                BlogEntity blog2 = BlogEntity.builder()
                        .title("Cách bảo quản trang sức vàng, bạc đúng cách")
                        .content("<p>Trang sức vàng, bạc là những món đồ quý giá cần được bảo quản đúng cách để giữ được độ bóng và vẻ đẹp ban đầu. Bài viết này sẽ hướng dẫn bạn cách bảo quản trang sức vàng, bạc đúng cách.</p><p>Đối với trang sức vàng, nên tránh tiếp xúc với hóa chất, nước hoa, mỹ phẩm. Sau khi sử dụng, nên lau sạch bằng khăn mềm và cất giữ trong hộp riêng.</p><p>Đối với trang sức bạc, nên tránh tiếp xúc với không khí để tránh bị oxy hóa. Nếu trang sức bạc bị xỉn màu, có thể sử dụng dung dịch làm sạch chuyên dụng hoặc nước chanh pha loãng để làm sạch.</p>")

                        .build();

                blogRepository.saveAll(Arrays.asList(blog1, blog2));
            }
        }
    }

}
