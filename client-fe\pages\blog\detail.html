<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chi tiết b<PERSON><PERSON> viết - <PERSON> Shop</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.min.css">
    <style>
        .blog-detail-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .blog-header {
            margin-bottom: 30px;
        }
        
        .blog-title {
            font-size: 2rem;
            margin-bottom: 15px;
        }
        
        .blog-meta {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
            color: #6c757d;
        }
        
        .blog-meta-item {
            margin-right: 20px;
            margin-bottom: 10px;
        }
        
        .blog-meta-item i {
            margin-right: 5px;
        }
        
        .blog-featured-image {
            width: 100%;
            height: auto;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .blog-content {
            line-height: 1.8;
            margin-bottom: 30px;
        }
        
        .blog-content p {
            margin-bottom: 20px;
        }
        
        .blog-content h2, .blog-content h3 {
            margin-top: 30px;
            margin-bottom: 15px;
        }
        
        .blog-content img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .blog-content ul, .blog-content ol {
            margin-bottom: 20px;
            padding-left: 20px;
        }
        
        .blog-content li {
            margin-bottom: 10px;
        }
        
        .blog-tags {
            margin-bottom: 30px;
        }
        
        .blog-tag {
            display: inline-block;
            background-color: #f8f9fa;
            padding: 5px 10px;
            border-radius: 5px;
            margin-right: 10px;
            margin-bottom: 10px;
            color: #333;
            transition: all 0.3s ease;
        }
        
        .blog-tag:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .blog-share {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .blog-share-title {
            margin-right: 15px;
            font-weight: 600;
        }
        
        .blog-share-links a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            color: white;
            transition: all 0.3s ease;
        }
        
        .blog-share-links a.facebook {
            background-color: #3b5998;
        }
        
        .blog-share-links a.twitter {
            background-color: #1da1f2;
        }
        
        .blog-share-links a.pinterest {
            background-color: #bd081c;
        }
        
        .blog-share-links a.linkedin {
            background-color: #0077b5;
        }
        
        .blog-share-links a:hover {
            opacity: 0.8;
            transform: translateY(-3px);
        }
        
        .blog-author {
            display: flex;
            align-items: center;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .blog-author-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-right: 20px;
        }
        
        .blog-author-info {
            flex-grow: 1;
        }
        
        .blog-author-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .blog-author-bio {
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        .blog-author-social a {
            color: #6c757d;
            margin-right: 10px;
            transition: all 0.3s ease;
        }
        
        .blog-author-social a:hover {
            color: var(--primary-color);
        }
        
        .blog-comments {
            margin-bottom: 30px;
        }
        
        .blog-comments-title {
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .comment {
            display: flex;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .comment:last-child {
            border-bottom: none;
        }
        
        .comment-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 15px;
        }
        
        .comment-content {
            flex-grow: 1;
        }
        
        .comment-author {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .comment-date {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .comment-text {
            margin-bottom: 10px;
        }
        
        .comment-reply {
            color: var(--primary-color);
            font-weight: 500;
            cursor: pointer;
        }
        
        .comment-form {
            margin-top: 30px;
        }
        
        .comment-form-title {
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .related-posts {
            margin-top: 50px;
        }
        
        .related-posts-title {
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .related-post {
            margin-bottom: 20px;
        }
        
        .related-post-img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .related-post-title {
            font-weight: 600;
            margin-bottom: 5px;
            font-size: 1rem;
        }
        
        .related-post-date {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Main Content -->
    <main class="container my-5">
        <div id="blog-detail-container">
            <!-- Blog detail will be loaded here via JavaScript -->
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Đang tải bài viết...</p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.all.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/api.js"></script>
    <script src="../../assets/js/common.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get blog post ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const postId = urlParams.get('id');
            
            if (!postId) {
                // Redirect to blog index if no ID provided
                window.location.href = 'index.html';
                return;
            }
            
            // Load blog post details
            loadBlogPostDetails(postId);
        });
        
        // Load blog post details
        async function loadBlogPostDetails(postId) {
            const blogDetailContainer = document.getElementById('blog-detail-container');
            try {
                const blogPost = await apiService.getBlogById(postId);
                // Generate blog detail HTML
                const blogDetailHTML = `
                    <div class="blog-detail-container">
                        <div class="blog-header">
                            <h1 class="blog-title">${blogPost.title}</h1>
                            <div class="blog-meta">
                                <div class="blog-meta-item">
                                    <i class="fas fa-calendar-alt"></i> ${formatDate(blogPost.createDate)}
                                </div>
                            </div>
                            <img src="${normalizeImageUrl(blogPost.image)}" alt="${blogPost.title}" class="blog-featured-image">
                        </div>
                        <div class="blog-content">
                            ${blogPost.content}
                        </div>
                    </div>
                `;
                blogDetailContainer.innerHTML = blogDetailHTML;
                document.title = blogPost.title + ' - Tina Shop';
            } catch (error) {
                blogDetailContainer.innerHTML = `<div class='alert alert-danger'>Không thể tải chi tiết bài viết.</div>`;
            }
        }
        
        // Helper
        function normalizeImageUrl(url) {
            if (!url) return '../../assets/image/default-blog.jpg';
            if (url.startsWith('http')) return url;
            return 'http://localhost:8080/' + url.replace(/^\/+/, '');
        }
    </script>
</body>
</html>
