<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog - Tina Shop</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.min.css">
    <style>
        .blog-banner {
            background-image: url('../../assets/image/blog-banner.jpg');
            background-size: cover;
            background-position: center;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin-bottom: 40px;
        }
        
        .blog-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .blog-banner-content {
            position: relative;
            color: white;
            text-align: center;
        }
        
        .blog-banner-title {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .blog-banner-subtitle {
            font-size: 1.2rem;
        }
        
        .blog-card {
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .blog-card:hover {
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }
        
        .blog-img-container {
            position: relative;
            height: 200px;
            overflow: hidden;
        }
        
        .blog-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        .blog-card:hover .blog-img {
            transform: scale(1.1);
        }
        
        .blog-date {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: var(--primary-color);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .blog-info {
            padding: 20px;
        }
        
        .blog-title {
            font-size: 1.2rem;
            margin-bottom: 10px;
            font-weight: 600;
            height: 2.8em;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        
        .blog-meta {
            display: flex;
            margin-bottom: 10px;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .blog-meta-item {
            margin-right: 15px;
        }
        
        .blog-meta-item i {
            margin-right: 5px;
        }
        
        .blog-excerpt {
            color: #6c757d;
            margin-bottom: 15px;
            height: 4.5em;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }
        
        .sidebar-widget {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
        }
        
        .sidebar-widget-title {
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .sidebar-categories {
            list-style: none;
            padding: 0;
        }
        
        .sidebar-categories li {
            margin-bottom: 10px;
        }
        
        .sidebar-categories li a {
            display: flex;
            justify-content: space-between;
            color: #333;
            transition: all 0.3s ease;
        }
        
        .sidebar-categories li a:hover {
            color: var(--primary-color);
        }
        
        .sidebar-categories li a span {
            background-color: #e9ecef;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
        }
        
        .recent-post {
            display: flex;
            margin-bottom: 15px;
        }
        
        .recent-post:last-child {
            margin-bottom: 0;
        }
        
        .recent-post-img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 5px;
            margin-right: 15px;
        }
        
        .recent-post-info {
            flex-grow: 1;
        }
        
        .recent-post-title {
            font-weight: 600;
            margin-bottom: 5px;
            font-size: 0.9rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .recent-post-date {
            color: #6c757d;
            font-size: 0.8rem;
        }
        
        .tag-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .tag {
            background-color: #e9ecef;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            color: #333;
            transition: all 0.3s ease;
        }
        
        .tag:hover {
            background-color: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Main Content -->
    <main>
        <!-- Blog Banner -->
        <div class="blog-banner">
            <div class="blog-banner-content">
                <h1 class="blog-banner-title">Blog Tina Shop</h1>
                <p class="blog-banner-subtitle">Cập nhật xu hướng thời trang mới nhất</p>
            </div>
        </div>
        
        <div class="container">
            <div class="row">
                <!-- Blog Posts -->
                <div class="col-lg-8">
                    <div class="row" id="blog-posts-container">
                        <!-- Blog posts will be loaded here via JavaScript -->
                        <div class="col-12 text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Đang tải bài viết...</p>
                        </div>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="Blog pagination" class="mt-4">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- Pagination will be generated via JavaScript -->
                        </ul>
                    </nav>
                </div>
                
                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Search Widget -->
                    <div class="sidebar-widget">
                        <h4 class="sidebar-widget-title">Tìm kiếm</h4>
                        <form class="search-form">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Tìm kiếm bài viết...">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Categories Widget -->
                    <div class="sidebar-widget">
                        <h4 class="sidebar-widget-title">Danh mục</h4>
                        <ul class="sidebar-categories">
                            <li><a href="#">Xu hướng thời trang <span>15</span></a></li>
                            <li><a href="#">Mẹo phối đồ <span>8</span></a></li>
                            <li><a href="#">Chăm sóc túi xách <span>12</span></a></li>
                            <li><a href="#">Sự kiện <span>6</span></a></li>
                            <li><a href="#">Khuyến mãi <span>10</span></a></li>
                        </ul>
                    </div>
                    
                    <!-- Recent Posts Widget -->
                    <div class="sidebar-widget">
                        <h4 class="sidebar-widget-title">Bài viết gần đây</h4>
                        <div id="recent-posts-container">
                            <!-- Recent posts will be loaded here -->
                        </div>
                    </div>
                    
                    <!-- Tags Widget -->
                    <div class="sidebar-widget">
                        <h4 class="sidebar-widget-title">Thẻ</h4>
                        <div class="tag-cloud">
                            <a href="#" class="tag">Thời trang</a>
                            <a href="#" class="tag">Túi xách</a>
                            <a href="#" class="tag">Ví</a>
                            <a href="#" class="tag">Phụ kiện</a>
                            <a href="#" class="tag">Xu hướng</a>
                            <a href="#" class="tag">Mùa hè</a>
                            <a href="#" class="tag">Mùa đông</a>
                            <a href="#" class="tag">Phong cách</a>
                            <a href="#" class="tag">Hàn Quốc</a>
                            <a href="#" class="tag">Châu Âu</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.all.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/api.js"></script>
    <script src="../../assets/js/common.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load blog posts
            loadBlogPosts();
            
            // Load recent posts
            loadRecentPosts();
        });
        
        // Load blog posts
        function loadBlogPosts(page = 1) {
            const blogPostsContainer = document.getElementById('blog-posts-container');
            blogPostsContainer.innerHTML = `<div class="col-12 text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">Đang tải bài viết...</p></div>`;
            apiService.getBlogs({ page, limit: 6 }).then(response => {
                const blogPosts = response.items || response.data || [];
                let html = '';
                blogPosts.forEach(post => {
                    html += `<div class="col-md-6 mb-4"><div class="blog-card h-100"><div class="blog-img-container"><img src="${post.image}" class="blog-img" alt="${post.title}"><div class="blog-date">${post.date || ''}</div></div><div class="blog-info"><div class="blog-title">${post.title}</div><div class="blog-meta"><div class="blog-meta-item"><i class="fas fa-user"></i> ${post.author || ''}</div><div class="blog-meta-item"><i class="fas fa-comments"></i> ${post.comments || 0}</div><div class="blog-meta-item"><i class="fas fa-eye"></i> ${post.views || 0}</div></div><div class="blog-excerpt">${post.excerpt || ''}</div><a href="detail.html?id=${post.id}" class="btn btn-outline-primary btn-sm">Đọc tiếp</a></div></div></div>`;
                });
                blogPostsContainer.innerHTML = html;
            }).catch(() => {
                blogPostsContainer.innerHTML = '<div class="col-12 text-center py-5 text-danger">Không thể tải bài viết.</div>';
            });
        }
        
        // Load recent posts
        function loadRecentPosts() {
            const recentPostsContainer = document.getElementById('recent-posts-container');
            recentPostsContainer.innerHTML = `<div class='text-center py-3'><div class='spinner-border text-primary' role='status'><span class='visually-hidden'>Loading...</span></div></div>`;
            apiService.getBlogs({ page: 1, limit: 3 }).then(response => {
                const recentPosts = response.items || response.data || [];
                let html = '';
                recentPosts.forEach(post => {
                    html += `<div class='recent-post'><img src='${post.image}' alt='${post.title}' class='recent-post-img'><div class='recent-post-info'><h5 class='recent-post-title'><a href='detail.html?id=${post.id}'>${post.title}</a></h5><div class='recent-post-date'>${post.date || ''}</div></div></div>`;
                });
                recentPostsContainer.innerHTML = html;
            }).catch(() => {
                recentPostsContainer.innerHTML = '<div class="text-danger">Không thể tải bài viết gần đây.</div>';
            });
        }
        
        // Generate pagination
        function generatePagination(currentPage, totalPages) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `
                <a class="page-link" href="#" aria-label="Previous" ${currentPage > 1 ? 'onclick="changePage(' + (currentPage - 1) + '); return false;"' : ''}>
                    <span aria-hidden="true">&laquo;</span>
                </a>
            `;
            pagination.appendChild(prevLi);
            
            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                const pageLi = document.createElement('li');
                pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
                pageLi.innerHTML = `
                    <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
                `;
                pagination.appendChild(pageLi);
            }
            
            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `
                <a class="page-link" href="#" aria-label="Next" ${currentPage < totalPages ? 'onclick="changePage(' + (currentPage + 1) + '); return false;"' : ''}>
                    <span aria-hidden="true">&raquo;</span>
                </a>
            `;
            pagination.appendChild(nextLi);
        }
        
        // Change page
        function changePage(page) {
            loadBlogPosts(page);
            window.scrollTo(0, 0);
        }
    </script>
</body>
</html>
