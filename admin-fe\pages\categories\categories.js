document.addEventListener('DOMContentLoaded', function() {
  // Kiểm tra đăng nhập
  if (!checkAuth()) {
    return;
  }

  // Load layout components
  loadLayoutComponents();

  // Khởi tạo biến toàn cục
  let categoryModal;
  let selectedImage = null;
  let categories = [];

  // Khởi tạo modal
  categoryModal = new bootstrap.Modal(document.getElementById('categoryModal'));

  // Load dữ liệu ban đầu
  loadCategories();

  // Xử lý sự kiện
  setupEventListeners();

  // Hàm load layout components
  function loadLayoutComponents() {
    // Load header
    fetch('/layout/header/header.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('header-container').innerHTML = html;

        // Load header script after HTML is inserted
        const headerScript = document.createElement('script');
        headerScript.src = '/layout/header/header.js';
        document.body.appendChild(headerScript);
      });

    // Load sidebar
    fetch('/layout/sidebar/sidebar.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('sidebar-container').innerHTML = html;

        // Load sidebar script after HTML is inserted
        const sidebarScript = document.createElement('script');
        sidebarScript.src = '/layout/sidebar/sidebar.js';
        document.body.appendChild(sidebarScript);
      });

    // Load footer
    fetch('/layout/footer/footer.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('footer-container').innerHTML = html;
      });
  }

  // Hàm load danh mục
  function loadCategories() {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    // Hiển thị loading
    document.getElementById('categoryTable').innerHTML = `
      <tr>
        <td colspan="7" class="text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Đang tải...</span>
          </div>
        </td>
      </tr>
    `;

    axios.get('http://localhost:8080/api/categories')
      .then(response => {
        categories = response.data;

        // Hiển thị tổng số danh mục
        document.getElementById('totalCategories').textContent = categories.length;

        // Hiển thị danh sách danh mục
        displayCategories(categories);

        // Cập nhật select box danh mục cha
        updateParentCategorySelect();
      })
      .catch(error => {
        handleApiError(error);

        // Hiển thị thông báo lỗi trong bảng
        document.getElementById('categoryTable').innerHTML = `
          <tr>
            <td colspan="7" class="text-center text-danger">
              Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại sau.
            </td>
          </tr>
        `;
      });
  }

  // Hàm hiển thị danh sách danh mục
  function displayCategories(categories) {
    const tableBody = document.getElementById('categoryTable');
    tableBody.innerHTML = '';

    if (categories.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="7" class="text-center">
            Không có danh mục nào
          </td>
        </tr>
      `;
      return;
    }

    // Lọc danh mục theo tìm kiếm
    const searchText = document.getElementById('searchCategory').value.toLowerCase();

    let filteredCategories = categories;

    if (searchText) {
      filteredCategories = filteredCategories.filter(category =>
        category.name.toLowerCase().includes(searchText) ||
        (category.description && category.description.toLowerCase().includes(searchText))
      );
    }

    // Hiển thị danh mục đã lọc
    filteredCategories.forEach((category, index) => {
      const row = document.createElement('tr');

      row.innerHTML = `
        <td>${index + 1}</td>
        <td>
          <img src="${category.image || '../../assets/image/default-image.avif'}" alt="${category.name}" class="img-thumbnail">
        </td>
        <td>${category.name}</td>
        <td>${category.description || 'N/A'}</td>
        <td>
          <button class="btn btn-sm btn-info me-1 btn-edit" data-id="${category.id}">
            <i class="bi bi-pencil"></i>
          </button>
          <button class="btn btn-sm btn-danger btn-delete" data-id="${category.id}">
            <i class="bi bi-trash"></i>
          </button>
        </td>
      `;

      tableBody.appendChild(row);
    });

    // Thêm sự kiện cho các nút
    document.querySelectorAll('.btn-edit').forEach(button => {
      button.addEventListener('click', function() {
        const categoryId = this.getAttribute('data-id');
        editCategory(categoryId);
      });
    });

    document.querySelectorAll('.btn-delete').forEach(button => {
      button.addEventListener('click', function() {
        const categoryId = this.getAttribute('data-id');
        deleteCategory(categoryId);
      });
    });
  }

  // Không cần cập nhật select box danh mục cha nữa
  function updateParentCategorySelect() {
    // Đã bỏ chức năng này vì không còn danh mục cha
  }

  // Hàm thiết lập các sự kiện
  function setupEventListeners() {
    // Sự kiện nút thêm danh mục
    document.getElementById('btnAddCategory').addEventListener('click', function() {
      resetCategoryForm();
      document.getElementById('categoryModalLabel').textContent = 'Thêm danh mục mới';
      categoryModal.show();
    });

    // Sự kiện nút lưu danh mục
    document.getElementById('btnSaveCategory').addEventListener('click', saveCategory);

    // Sự kiện nút tìm kiếm
    document.getElementById('btnSearch').addEventListener('click', function() {
      displayCategories(categories);
    });

    // Đã bỏ sự kiện thay đổi bộ lọc và tạo slug

    // Sự kiện chọn hình ảnh
    document.getElementById('categoryImage').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        // Kiểm tra kích thước file (giới hạn 2MB)
        if (file.size > 2 * 1024 * 1024) {
          showErrorToast('Kích thước ảnh không được vượt quá 2MB');
          this.value = '';
          return;
        }

        // Kiểm tra loại file
        if (!file.type.match('image.*')) {
          showErrorToast('Vui lòng chọn file ảnh');
          this.value = '';
          return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
          // Chỉ lưu trữ ảnh để hiển thị preview
          const previewContainer = document.getElementById('imagePreview');
          previewContainer.innerHTML = `<img src="${e.target.result}" class="image-preview">`;

          // Lưu file để tải lên sau
          selectedImage = file;
        };
        reader.readAsDataURL(file);
      }
    });
  }

  // Hàm reset form danh mục
  function resetCategoryForm() {
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryId').value = '';
    document.getElementById('imagePreview').innerHTML = '';
    selectedImage = null;
  }

  // Hàm lưu danh mục
  function saveCategory() {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    // Lấy dữ liệu từ form
    const categoryId = document.getElementById('categoryId').value;
    const name = document.getElementById('categoryName').value;
    const description = document.getElementById('categoryDescription').value;

    // Kiểm tra dữ liệu bắt buộc
    if (!name) {
      showErrorToast('Vui lòng nhập tên danh mục');
      return;
    }

    // Tạo đối tượng dữ liệu cơ bản
    const categoryData = {
      name,
      description,
      image: null // Sẽ được cập nhật sau khi tải ảnh lên
    };

    // Hàm xử lý việc gửi dữ liệu danh mục sau khi đã xử lý ảnh
    const submitCategoryData = (imageUrl) => {
      // Cập nhật đường dẫn ảnh nếu có
      if (imageUrl) {
        categoryData.image = imageUrl;
      }

      // Gọi API tạo/cập nhật danh mục
      if (categoryId) {
        // Cập nhật danh mục
        axios.put(`http://localhost:8080/api/categories/${categoryId}`, categoryData)
          .then(() => {
            showSuccessToast('Cập nhật danh mục thành công');
            categoryModal.hide();
            loadCategories();
          })
          .catch(error => {
            handleApiError(error);
          });
      } else {
        // Tạo danh mục mới
        axios.post('http://localhost:8080/api/categories', categoryData)
          .then(() => {
            showSuccessToast('Thêm danh mục thành công');
            categoryModal.hide();
            loadCategories();
          })
          .catch(error => {
            handleApiError(error);
          });
      }
    };

    // Kiểm tra xem có ảnh mới được chọn không
    if (selectedImage instanceof File) {
      // Tạo FormData để tải ảnh lên
      const formData = new FormData();
      formData.append('file', selectedImage);

      // Hiển thị thông báo đang tải ảnh
      showInfoToast('Đang tải ảnh lên...');

      // Gọi API tải ảnh lên
      axios.post('http://localhost:8080/files/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
        .then(response => {
          // Lấy đường dẫn ảnh từ response
          const imageUrl = response.data;
          // Tiếp tục gửi dữ liệu danh mục với đường dẫn ảnh mới
          submitCategoryData(imageUrl);
        })
        .catch(error => {
          handleApiError(error);
          showErrorToast('Lỗi khi tải ảnh lên. Vui lòng thử lại.');
        });
    } else {
      // Không có ảnh mới, sử dụng ảnh hiện tại hoặc null
      submitCategoryData(selectedImage);
    }
  }

  // Hàm hiển thị thông báo thông tin
  function showInfoToast(message) {
    Swal.fire({
      icon: 'info',
      title: 'Thông báo',
      text: message,
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000
    });
  }

  // Hàm sửa danh mục
  function editCategory(categoryId) {
    const category = categories.find(c => c.id === parseInt(categoryId));
    if (!category) {
      showErrorToast('Không tìm thấy danh mục');
      return;
    }

    // Điền dữ liệu vào form
    document.getElementById('categoryId').value = category.id;
    document.getElementById('categoryName').value = category.name;
    document.getElementById('categoryDescription').value = category.description || '';

    // Hiển thị hình ảnh
    if (category.image) {
      // Lưu đường dẫn ảnh hiện tại
      selectedImage = category.image;
      document.getElementById('imagePreview').innerHTML = `
        <img src="${category.image}" class="image-preview">
      `;
    } else {
      selectedImage = null;
      document.getElementById('imagePreview').innerHTML = '';
    }

    // Hiển thị modal
    document.getElementById('categoryModalLabel').textContent = 'Cập nhật danh mục';
    categoryModal.show();
  }

  // Hàm xóa danh mục
  function deleteCategory(categoryId) {
    // Kiểm tra xem danh mục có tồn tại không
    const category = categories.find(c => c.id === parseInt(categoryId));
    if (!category) {
      showErrorToast('Không tìm thấy danh mục');
      return;
    }

    // Hiển thị xác nhận xóa
    showConfirmDialog(
      'Xác nhận xóa',
      'Bạn có chắc chắn muốn xóa danh mục này không?',
      function() {
        // Hiển thị thông báo đang xử lý
        showInfoToast('Đang xử lý...');

        const token = localStorage.getItem('token');

        // Thiết lập headers với đầy đủ thông tin
        const headers = {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        };

        // Sử dụng cấu hình đầy đủ cho request
        axios({
          method: 'DELETE',
          url: `http://localhost:8080/api/categories/${categoryId}`,
          headers: headers
        })
          .then(() => {
            showSuccessToast('Xóa danh mục thành công');
            loadCategories();
          })
          .catch(error => {
            console.error('Lỗi khi xóa danh mục:', error);

            // Xử lý lỗi cụ thể
            if (error.response && error.response.data) {
              const errorData = error.response.data;
              const errorMessage = errorData.message || 'Lỗi không xác định';
              const errorCode = errorData.code;

              if (errorMessage.includes('CATEGORY_HAS_PRODUCTS') || errorCode === 2005) {
                showErrorToast('Không thể xóa danh mục này vì có sản phẩm thuộc danh mục');
              } else {
                showErrorToast(`Lỗi: ${errorMessage}`);
              }
            } else {
              showErrorToast('Lỗi khi xóa danh mục. Vui lòng thử lại sau.');
            }
          });
      }
    );
  }
});
