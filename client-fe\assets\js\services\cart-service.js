// Cart Service for Client

// Cart API
const CartService = {
    getByUserId: async (userId) => {
        return await apiRequest(`api/carts/user/${userId}`);
    },

    addItem: async (cartItemData) => {
        return await apiRequest(`api/carts/create`, 'POST', cartItemData);
    },

    updateItem: async (itemId, quantity) => {
        return await apiRequest(`api/carts/update/${itemId}`, 'PUT', { quantity });
    },

    removeItem: async (itemId) => {
        return await apiRequest(`api/carts/delete/${itemId}`, 'DELETE');
    },

    clear: async (userId) => {
        return await apiRequest(`api/carts/delete-all/${userId}`, 'DELETE');
    },
    
    // Local storage cart functions for non-authenticated users
    getLocalCart: () => {
        return JSON.parse(localStorage.getItem('cart')) || [];
    },
    
    saveLocalCart: (cart) => {
        localStorage.setItem('cart', JSON.stringify(cart));
    },
    
    addLocalItem: (productId, quantity = 1, variantId = null) => {
        const cart = CartService.getLocalCart();
        
        // Check if product already exists in cart
        const existingItemIndex = cart.findIndex(item => 
            item.productId === productId && item.variantId === variantId
        );
        
        if (existingItemIndex !== -1) {
            // Update quantity if product already exists
            cart[existingItemIndex].quantity += quantity;
        } else {
            // Add new item to cart
            cart.push({
                productId,
                variantId,
                quantity
            });
        }
        
        CartService.saveLocalCart(cart);
        return cart;
    },
    
    updateLocalItem: (productId, quantity, variantId = null) => {
        const cart = CartService.getLocalCart();
        
        // Find the item
        const existingItemIndex = cart.findIndex(item => 
            item.productId === productId && item.variantId === variantId
        );
        
        if (existingItemIndex !== -1) {
            // Update quantity
            cart[existingItemIndex].quantity = quantity;
            CartService.saveLocalCart(cart);
        }
        
        return cart;
    },
    
    removeLocalItem: (productId, variantId = null) => {
        let cart = CartService.getLocalCart();
        
        // Remove the item
        cart = cart.filter(item => 
            !(item.productId === productId && item.variantId === variantId)
        );
        
        CartService.saveLocalCart(cart);
        return cart;
    },
    
    clearLocalCart: () => {
        localStorage.removeItem('cart');
        return [];
    },
    
    getLocalCartCount: () => {
        const cart = CartService.getLocalCart();
        return cart.reduce((total, item) => total + item.quantity, 0);
    },
    
    // Sync local cart with server when user logs in
    syncCartWithServer: async (userId) => {
        const localCart = CartService.getLocalCart();
        
        if (localCart.length === 0) {
            return;
        }
        
        try {
            // Add each local cart item to server
            for (const item of localCart) {
                await CartService.addItem(userId, {
                    productId: item.productId,
                    variantId: item.variantId,
                    quantity: item.quantity
                });
            }
            
            // Clear local cart after syncing
            CartService.clearLocalCart();
        } catch (error) {
            console.error('Error syncing cart with server:', error);
            throw error;
        }
    }
};
