document.addEventListener('DOMContentLoaded', function() {
  // Kiểm tra đăng nhập
  if (!checkAuth()) {
    return;
  }
  
  // Load layout components
  loadLayoutComponents();
  
  // Khởi tạo biến toàn cục
  let currentPage = 0;
  let pageSize = 10;
  let totalPages = 0;
  
  // Load dữ liệu ban đầu
  loadUsers(currentPage, pageSize);
  
  // Xử lý sự kiện
  setupEventListeners();
  
  // Khởi tạo modal user
  let userModal;
  setTimeout(() => {
    const modalElement = document.getElementById('userModal');
    if (modalElement) {
      userModal = new bootstrap.Modal(modalElement);
      setupUserModalEvents();
    }
  }, 500);
  
  function setupUserModalEvents() {
    // Mở modal khi bấm Thêm người dùng
    const btnAddUser = document.getElementById('btnAddUser');
    if (btnAddUser) {
      btnAddUser.addEventListener('click', () => {
        resetUserForm();
        const modalLabel = document.getElementById('userModalLabel');
        if (modalLabel) modalLabel.textContent = 'Thêm người dùng';
        if (userModal) userModal.show();
      });
    }
    // Xử lý upload ảnh preview
    const avatarInput = document.getElementById('avatar');
    if (avatarInput) {
      avatarInput.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            const preview = document.getElementById('avatarPreview');
            if (preview) preview.innerHTML = `<img src="${e.target.result}" class="img-thumbnail" style="max-height: 120px;">`;
          };
          reader.readAsDataURL(file);
        }
      });
    }
    // Lưu user
    const btnSaveUser = document.getElementById('btnSaveUser');
    if (btnSaveUser) {
      btnSaveUser.addEventListener('click', saveUser);
    }
  }
  
  function resetUserForm() {
    const form = document.getElementById('userForm');
    if (form) form.reset();
    const preview = document.getElementById('avatarPreview');
    if (preview) preview.innerHTML = '';
  }
  
  async function saveUser() {
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;
    const fullname = document.getElementById('fullname').value;
    const phone = document.getElementById('phone').value;
    const address = document.getElementById('address').value;
    const password = document.getElementById('password').value;
    const gender = document.getElementById('gender').value;
    const roleName = document.getElementById('roleName').value;
    let avatar = null;
    const avatarInput = document.getElementById('avatar');
    if (avatarInput && avatarInput.files && avatarInput.files[0]) {
      const formData = new FormData();
      formData.append('file', avatarInput.files[0]);
      try {
        const uploadRes = await axios.post('http://localhost:8080/files/upload', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
        avatar = uploadRes.data;
      } catch (err) {
        Swal.fire({ icon: 'error', title: 'Lỗi', text: 'Upload ảnh thất bại!' });
        return;
      }
    }
    const userData = {
      username,
      email,
      fullname,
      phone,
      avatar,
      address,
      password,
      gender,
      roleName
    };
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    axios.post('http://localhost:8080/api/users', userData)
      .then(response => {
        Swal.fire({ icon: 'success', title: 'Thành công', text: 'Thêm người dùng thành công' }).then(() => {
          if (userModal) userModal.hide();
          loadUsers(currentPage, pageSize);
        });
      })
      .catch(error => {
        handleApiError(error);
      });
  }
  
  // Hàm load layout components
  function loadLayoutComponents() {
    // Load header
    fetch('../../layout/header/header.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('header-container').innerHTML = html;
        
        // Load header script after HTML is inserted
        const headerScript = document.createElement('script');
        headerScript.src = '../../layout/header/header.js';
        document.body.appendChild(headerScript);
      });
    
    // Load sidebar
    fetch('../../layout/sidebar/sidebar.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('sidebar-container').innerHTML = html;
        
        // Load sidebar script after HTML is inserted
        const sidebarScript = document.createElement('script');
        sidebarScript.src = '../../layout/sidebar/sidebar.js';
        document.body.appendChild(sidebarScript);
      });
    
    // Load footer
    fetch('../../layout/footer/footer.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('footer-container').innerHTML = html;
      });
  }
  
  // Hàm load người dùng
  function loadUsers(page, size, role = '', status = '', keyword = '') {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    // Hiển thị loading
    document.getElementById('userTable').innerHTML = `
      <tr>
        <td colspan="8" class="text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Đang tải...</span>
          </div>
        </td>
      </tr>
    `;
    
    // Xây dựng URL với các tham số lọc
    let url = `http://localhost:8080/api/users?page=${page}&size=${size}`;
    
    if (keyword) {
      url = `http://localhost:8080/api/users/search?keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`;
    }
    
    axios.get(url)
      .then(response => {
        let users;
        let totalElements;
        
        // Kiểm tra cấu trúc phản hồi
        if (response.data.content) {
          // Phản hồi dạng Page
          users = response.data.content;
          totalElements = response.data.totalElements;
          totalPages = response.data.totalPages;
        } else {
          // Phản hồi dạng List
          users = response.data;
          totalElements = users.length;
          totalPages = 1;
        }
        
        // Lọc theo vai trò nếu có
        if (role) {
          users = users.filter(user => user.role === role);
          totalElements = users.length;
        }
        
        // Lọc theo trạng thái nếu có
        if (status !== '') {
          users = users.filter(user => user.status.toString() === status);
          totalElements = users.length;
        }
        
        // Hiển thị tổng số người dùng
        document.getElementById('totalUsers').textContent = totalElements;
        
        // Hiển thị danh sách người dùng
        displayUsers(users);
        
        // Hiển thị phân trang
        displayPagination(page, totalPages);
      })
      .catch(error => {
        handleApiError(error);
        
        // Hiển thị thông báo lỗi trong bảng
        document.getElementById('userTable').innerHTML = `
          <tr>
            <td colspan="8" class="text-center text-danger">
              Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại sau.
            </td>
          </tr>
        `;
      });
  }
  
  // Hàm hiển thị danh sách người dùng
  function displayUsers(users) {
    const tableBody = document.getElementById('userTable');
    tableBody.innerHTML = '';
    
    if (users.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="8" class="text-center">
            Không có người dùng nào
          </td>
        </tr>
      `;
      return;
    }
    
    users.forEach((user, index) => {
      const row = document.createElement('tr');
      
      // Tạo badge cho vai trò
      const roleBadge = user.role === 'ADMIN' 
        ? '<span class="badge badge-admin">Admin</span>' 
        : '<span class="badge badge-user">Người dùng</span>';
      
      // Tạo badge cho trạng thái
      const statusBadge = user.status 
        ? '<span class="badge badge-active">Hoạt động</span>' 
        : '<span class="badge badge-inactive">Bị khóa</span>';
      
      row.innerHTML = `
        <td>${index + 1 + currentPage * pageSize}</td>
        <td>
          <img src="${user.avatar || '../../assets/images/default-avatar.jpg'}" alt="${user.fullname}" class="rounded-circle img-thumbnail">
        </td>
        <td>${user.fullname}</td>
        <td>${user.email}</td>
        <td>${user.phone || 'N/A'}</td>
        <td>${roleBadge}</td>
        <td>
          <button class="btn btn-sm btn-info me-1 btn-view" data-id="${user.id}">
            <i class="bi bi-eye"></i>
          </button>
         
        </td>
      `;
      
      tableBody.appendChild(row);
    });
    
    // Thêm sự kiện cho các nút
    document.querySelectorAll('.btn-view').forEach(button => {
      button.addEventListener('click', function() {
        const userId = this.getAttribute('data-id');
        viewUser(userId);
      });
    });
    
    document.querySelectorAll('.btn-toggle-status').forEach(button => {
      button.addEventListener('click', function() {
        const userId = this.getAttribute('data-id');
        const currentStatus = this.getAttribute('data-status') === 'true';
        toggleUserStatus(userId, currentStatus);
      });
    });
  }
  
  // Hàm hiển thị phân trang
  function displayPagination(currentPage, totalPages) {
    const paginationContainer = document.getElementById('pagination');
    paginationContainer.innerHTML = '';
    
    if (totalPages <= 1) {
      return;
    }
    
    const pagination = createPagination(currentPage, totalPages, (page) => {
      currentPage = page;
      loadUsers(
        page, 
        pageSize, 
        document.getElementById('filterRole').value,
        document.getElementById('filterStatus').value,
        document.getElementById('searchUser').value
      );
    });
    
    paginationContainer.appendChild(pagination);
  }
  
  // Hàm thiết lập các sự kiện
  function setupEventListeners() {
    // Sự kiện nút tìm kiếm
    document.getElementById('btnSearch').addEventListener('click', function() {
      currentPage = 0;
      loadUsers(
        currentPage, 
        pageSize, 
        document.getElementById('filterRole').value,
        document.getElementById('filterStatus').value,
        document.getElementById('searchUser').value
      );
    });
    
    // Sự kiện thay đổi bộ lọc
    document.getElementById('filterRole').addEventListener('change', function() {
      currentPage = 0;
      loadUsers(
        currentPage, 
        pageSize, 
        this.value,
        document.getElementById('filterStatus').value,
        document.getElementById('searchUser').value
      );
    });
    
    document.getElementById('filterStatus').addEventListener('change', function() {
      currentPage = 0;
      loadUsers(
        currentPage, 
        pageSize, 
        document.getElementById('filterRole').value,
        this.value,
        document.getElementById('searchUser').value
      );
    });
  }
  
  // Hàm xem thông tin người dùng
  function viewUser(userId) {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    axios.get(`http://localhost:8080/api/users/${userId}`)
      .then(response => {
        const user = response.data;
        
        Swal.fire({
          title: 'Thông tin người dùng',
          html: `
            <div class="text-center mb-3">
              <img src="${user.avatar || '../../assets/images/default-avatar.jpg'}" alt="${user.fullname}" class="rounded-circle" style="width: 100px; height: 100px; object-fit: cover;">
            </div>
            <div class="text-start">
              <p><strong>Họ tên:</strong> ${user.fullname}</p>
              <p><strong>Email:</strong> ${user.email}</p>
              <p><strong>Số điện thoại:</strong> ${user.phone || 'N/A'}</p>
              <p><strong>Địa chỉ:</strong> ${user.address || 'N/A'}</p>
              <p><strong>Vai trò:</strong> ${user.role === 'ADMIN' ? 'Admin' : 'Người dùng'}</p>
              <p><strong>Trạng thái:</strong> ${user.status ? 'Hoạt động' : 'Bị khóa'}</p>
              <p><strong>Ngày tạo:</strong> ${formatDate(user.createDate)}</p>
            </div>
          `,
          confirmButtonText: 'Đóng'
        });
      })
      .catch(error => {
        handleApiError(error);
      });
  }
  
  // Hàm thay đổi trạng thái người dùng
  function toggleUserStatus(userId, currentStatus) {
    const newStatus = !currentStatus;
    const action = newStatus ? 'mở khóa' : 'khóa';
    
    showConfirmDialog(
      `Xác nhận ${action} người dùng`,
      `Bạn có chắc chắn muốn ${action} người dùng này không?`,
      function() {
        const token = localStorage.getItem('token');
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        
        axios.put(`http://localhost:8080/api/users/${userId}/status`, { status: newStatus })
          .then(response => {
            showSuccessToast(`${action.charAt(0).toUpperCase() + action.slice(1)} người dùng thành công`);
            loadUsers(currentPage, pageSize);
          })
          .catch(error => {
            handleApiError(error);
          });
      }
    );
  }
});
