package com.project.codebasespringjpa.repository;

import com.project.codebasespringjpa.entity.BlogEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface IBlogRepository extends JpaRepository<BlogEntity, Long> {
    @Query("""
    select bl from BlogEntity bl where bl.isDelete = false 
    and (:keyword is null or bl.title like concat('%', :keyword, '%') ) 
    order by bl.createDate desc 
""")
    Page<BlogEntity> findAll(@Param("keyword") String keyword,
                            Pageable pageable);

    boolean existsByTitle(String title);
}
