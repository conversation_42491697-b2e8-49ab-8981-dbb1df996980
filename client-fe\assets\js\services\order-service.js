// Order Service for Client

// Order API
const OrderService = {
    getByUserId: async (userId, params = {}) => {
        const query = new URLSearchParams({...params, userId}).toString();
        return await apiRequest(`orders/find-all?${query}`);
    },

    getById: async (id) => {
        return await apiRequest(`orders/${id}`);
    },

    create: async (orderData) => {
        return await apiRequest('orders/create', 'POST', orderData);
    },

    updateStatus: async (id, status) => {
        return await apiRequest(`orders/change-status/${id}`, 'PUT', { status });
    },

    getOrderDetails: async (orderId) => {
        return await apiRequest(`order-details/order/${orderId}`);
    }
};
