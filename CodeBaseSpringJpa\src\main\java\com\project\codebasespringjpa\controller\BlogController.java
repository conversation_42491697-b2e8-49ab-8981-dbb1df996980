package com.project.codebasespringjpa.controller;

import com.project.codebasespringjpa.dto.blog.request.BlogRequest;
import com.project.codebasespringjpa.dto.blog.response.BlogResponse;
import com.project.codebasespringjpa.service.interfaces.IBlogService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/blogs")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class BlogController {
    IBlogService blogService;

    @GetMapping("/find-all")
    public ResponseEntity<Page<BlogResponse>> findAll(@RequestParam(name = "keyword", required = false) String keyword,
                                                        @RequestParam(name = "limit", defaultValue = "5")Integer limit,
                                                        @RequestParam(name = "page", defaultValue = "1") Integer page) {

        Pageable pageable = PageRequest.of(page-1, limit);
        return ResponseEntity.ok(blogService.findAll(keyword, pageable));
    }

    @GetMapping("/{id}")
    public ResponseEntity<BlogResponse> findById(@PathVariable Long id) {
        return ResponseEntity.ok(blogService.findById(id));
    }


    @PostMapping("/create")
    public ResponseEntity<BlogResponse> create(@RequestBody BlogRequest request) {
        return new ResponseEntity<>(blogService.create(request), HttpStatus.CREATED);
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<BlogResponse> update(@PathVariable Long id, @RequestBody BlogRequest request) {
        return ResponseEntity.ok(blogService.update(id, request));
    }


    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        blogService.delete(id);
        return ResponseEntity.noContent().build();
    }
}
