package com.project.codebasespringjpa.util;

import org.springframework.util.StringUtils;

/**
 * Lớp tiện ích để xử lý ảnh
 */
public class ImageUtil {
    
    private static final String IMAGE_BASE_URL = "http://localhost:8080/";
    
    /**
     * Xử lý đường dẫn ảnh, nếu null hoặc rỗng thì trả về ảnh mặc định
     * Nếu không, thêm tiền tố URL vào đường dẫn ảnh
     * 
     * @param imagePath đường dẫn ảnh
     * @return đường dẫn ảnh đã xử lý
     */
    public static String processImageUrl(String imagePath) {
        if (!StringUtils.hasText(imagePath)) {
            return IMAGE_BASE_URL + UtilVariable.IMAGE_DEFAULT;
        }
        
        // Nếu đường dẫn đã có http:// hoặc https:// thì không thêm tiền tố
        if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
            return imagePath;
        }
        
        // Loại bỏ dấu / ở đầu nếu có
        String path = imagePath;
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        
        return IMAGE_BASE_URL + path;
    }
}
