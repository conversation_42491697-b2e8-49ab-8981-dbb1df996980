package com.project.codebasespringjpa.service.interfaces;

import com.project.codebasespringjpa.dto.category.request.CategoryCreateRequest;
import com.project.codebasespringjpa.dto.category.request.CategoryUpdateRequest;
import com.project.codebasespringjpa.dto.category.response.CategoryResponse;

import java.util.List;

public interface ICategoryService {
    List<CategoryResponse> findAll();
    CategoryResponse findById(Long id);
    List<CategoryResponse> findMainCategories();
    CategoryResponse create(CategoryCreateRequest request);
    CategoryResponse update(Long id, CategoryUpdateRequest request);
    void delete(Long id);
    boolean existsByName(String name);
}
