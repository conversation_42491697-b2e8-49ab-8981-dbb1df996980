package com.project.codebasespringjpa.service.interfaces;

import com.project.codebasespringjpa.dto.order.request.OrderRequest;
import com.project.codebasespringjpa.dto.order.request.OrderSearch;
import com.project.codebasespringjpa.dto.order.response.OrderResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

public interface IOrderService {
    Page<OrderResponse> findAll(OrderSearch orderSearch, Pageable pageable);
    OrderResponse findById(Long id);

    Long countByStatus(String status);
    Double getTotalRevenue();
    Double getRevenueBetween(LocalDateTime startDate, LocalDateTime endDate);
    OrderResponse create(OrderRequest request);
    OrderResponse updateStatus(Long id, String  status);
    void delete(Long id);
}
