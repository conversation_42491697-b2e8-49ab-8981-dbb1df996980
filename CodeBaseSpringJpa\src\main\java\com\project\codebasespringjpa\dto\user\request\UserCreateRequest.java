package com.project.codebasespringjpa.dto.user.request;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserCreateRequest {
    String username;
    String email;
    String fullname;
    String phone;
    String avatar;
    String address;
    String password;
    String gender;
    String roleName; // Tên của role
}
