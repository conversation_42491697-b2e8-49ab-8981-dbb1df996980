package com.project.codebasespringjpa.service.interfaces;

import com.project.codebasespringjpa.dto.wishlist.request.WishlistCreateRequest;
import com.project.codebasespringjpa.dto.wishlist.response.WishlistResponse;

import java.util.List;

public interface IWishlistService {
    List<WishlistResponse> findAll();
    WishlistResponse findById(Long id);
    List<WishlistResponse> findByUserId(Long userId);
    WishlistResponse create(WishlistCreateRequest request);
    void delete(Long id);
    boolean existsByUserIdAndProductId(Long userId, Long productId);
}
