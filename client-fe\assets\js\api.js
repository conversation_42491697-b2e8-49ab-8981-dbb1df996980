// API Configuration
const API_BASE_URL = 'http://localhost:8080';
const API_ENDPOINTS = {
    // Auth
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',

    // Products
    PRODUCTS: '/api/products',
    PRODUCT_DETAIL: '/api/products/', // + id
    FEATURED_PRODUCTS: '/api/products/featured',
    PRODUCTS_BY_CATEGORY: '/api/products/category/', // + categoryId

    // Categories
    CATEGORIES: '/api/categories',

    // Cart
    CART: '/api/cart',
    ADD_TO_CART: '/api/cart/add',
    UPDATE_CART: '/api/cart/update',
    REMOVE_FROM_CART: '/api/cart/remove',

    // Orders
    ORDERS: '/api/orders',
    ORDER_DETAIL: '/api/orders/', // + id
    CREATE_ORDER: '/api/orders/create',

    // User
    USER_PROFILE: '/api/user/profile',
    UPDATE_PROFILE: '/api/user/profile/update',

    // Blog
    BLOG_POSTS: '/api/blogs',
    BLOG_POST_DETAIL: '/api/blogs/', // + id
    FEATURED_BLOG_POSTS: '/api/blogs/featured',

    // Location
    PROVINCES: 'https://open.oapi.vn/location/provinces',
    DISTRICTS: 'https://open.oapi.vn/location/districts/', // + provinceId
    WARDS: 'https://open.oapi.vn/location/wards/' // + districtId
};

// Mock data for frontend development
const MOCK_DATA = {
    // Products
    products: [
        {
            id: 1,
            name: 'Túi xách thời trang cao cấp Tina',
            price: 1290000,
            salePrice: 990000,
            description: 'Túi xách thời trang cao cấp Tina được thiết kế tinh tế với chất liệu da cao cấp, bền đẹp theo thời gian.',
            images: [
                'https://i.imgur.com/JFS83x3.jpg',
                'https://i.imgur.com/8koJZWx.png',
                'https://i.imgur.com/JFS83x3.jpg',
                'https://i.imgur.com/8koJZWx.png'
            ],
            thumbnail: 'https://i.imgur.com/JFS83x3.jpg',
            category: 'bags',
            rating: 4.5,
            reviewCount: 12
        },
        {
            id: 2,
            name: 'Ví cầm tay nữ da thật',
            price: 850000,
            salePrice: 680000,
            description: 'Ví cầm tay nữ được làm từ da bò thật 100%, thiết kế hiện đại, sang trọng.',
            images: [
                'https://i.imgur.com/8koJZWx.png',
                'https://i.imgur.com/JFS83x3.jpg',
                'https://i.imgur.com/8koJZWx.png',
                'https://i.imgur.com/JFS83x3.jpg'
            ],
            thumbnail: 'https://i.imgur.com/8koJZWx.png',
            category: 'wallets',
            rating: 5,
            reviewCount: 8
        },
        {
            id: 3,
            name: 'Túi đeo chéo mini phong cách Hàn Quốc',
            price: 750000,
            description: 'Túi đeo chéo mini với thiết kế nhỏ gọn, phong cách Hàn Quốc, phù hợp cho các bạn trẻ.',
            images: [
                'https://i.imgur.com/JFS83x3.jpg',
                'https://i.imgur.com/8koJZWx.png',
                'https://i.imgur.com/JFS83x3.jpg',
                'https://i.imgur.com/8koJZWx.png'
            ],
            thumbnail: 'https://i.imgur.com/JFS83x3.jpg',
            category: 'bags',
            rating: 4,
            reviewCount: 6
        },
        {
            id: 4,
            name: 'Bộ phụ kiện thời trang cao cấp',
            price: 1500000,
            salePrice: 1200000,
            description: 'Bộ phụ kiện thời trang cao cấp bao gồm thắt lưng, ví và móc khóa, làm từ chất liệu da cao cấp.',
            images: [
                'https://i.imgur.com/8koJZWx.png',
                'https://i.imgur.com/JFS83x3.jpg',
                'https://i.imgur.com/8koJZWx.png',
                'https://i.imgur.com/JFS83x3.jpg'
            ],
            thumbnail: 'https://i.imgur.com/8koJZWx.png',
            category: 'accessories',
            rating: 4.5,
            reviewCount: 4
        }
    ],

    // Categories
    categories: [
        { id: 1, name: 'Túi xách', slug: 'bags', count: 24 },
        { id: 2, name: 'Ví', slug: 'wallets', count: 18 },
        { id: 3, name: 'Phụ kiện', slug: 'accessories', count: 32 }
    ],

    // Blog posts
    blogPosts: [
        {
            id: 1,
            title: 'Xu hướng thời trang túi xách mùa hè 2023',
            excerpt: 'Mùa hè 2023 chứng kiến sự trở lại của những chiếc túi xách mini với màu sắc tươi sáng. Các thương hiệu lớn đều cho ra mắt những bộ sưu tập với gam màu pastel...',
            content: 'Nội dung chi tiết của bài viết...',
            imageUrl: 'https://i.imgur.com/JFS83x3.jpg',
            date: '2023-05-15',
            author: 'Nguyễn Thị Hương',
            category: 'Xu hướng thời trang',
            comments: 8,
            views: 245
        },
        {
            id: 2,
            title: 'Cách phối đồ với các loại túi xách khác nhau',
            excerpt: 'Túi xách không chỉ là phụ kiện đi kèm mà còn là điểm nhấn cho trang phục của bạn. Bài viết này sẽ hướng dẫn cách phối đồ với từng loại túi xách...',
            content: 'Nội dung chi tiết của bài viết...',
            imageUrl: 'https://i.imgur.com/8koJZWx.png',
            date: '2023-05-10',
            author: 'Trần Minh Anh',
            category: 'Mẹo phối đồ',
            comments: 12,
            views: 320
        }
    ]
};

// Axios Configuration
const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
});

// Add request interceptor to include auth token
api.interceptors.request.use(
    config => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

// Add response interceptor to handle common errors
api.interceptors.response.use(
    response => {
        return response;
    },
    error => {
        if (error.response) {
            // Handle 401 Unauthorized
            if (error.response.status === 401) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');

                // Redirect to login if not already there
                if (!window.location.href.includes('login')) {
                    Swal.fire({
                        title: 'Phiên đăng nhập hết hạn',
                        text: 'Vui lòng đăng nhập lại',
                        icon: 'warning',
                        confirmButtonText: 'Đăng nhập'
                    }).then(() => {
                        window.location.href = '/pages/login/index.html';
                    });
                }
            }

            // Handle 403 Forbidden
            if (error.response.status === 403) {
                Swal.fire({
                    title: 'Không có quyền truy cập',
                    text: 'Bạn không có quyền thực hiện hành động này',
                    icon: 'error',
                    confirmButtonText: 'Đóng'
                });
            }

            // Handle 500 Server Error
            if (error.response.status >= 500) {
                Swal.fire({
                    title: 'Lỗi máy chủ',
                    text: 'Đã xảy ra lỗi, vui lòng thử lại sau',
                    icon: 'error',
                    confirmButtonText: 'Đóng'
                });
            }
        } else if (error.request) {
            // Network error
            Swal.fire({
                title: 'Lỗi kết nối',
                text: 'Không thể kết nối đến máy chủ, vui lòng kiểm tra kết nối mạng',
                icon: 'error',
                confirmButtonText: 'Đóng'
            });
        }

        return Promise.reject(error);
    }
);

// API Functions
const apiService = {
    // Auth
    login: async (username, password) => {
        try {
            const response = await axios.post(`${API_BASE_URL}/auth/login`, { username, password });
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    register: async (userData) => {
        try {
            const response = await axios.post(`${API_BASE_URL}/auth/register`, userData);
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    logout: () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/index.html';
    },

    // Products
    getProducts: async (params = {}) => {
        // params: { keyword, categoryName, suplierName, categoryId, supplierId, page, limit }
        const query = new URLSearchParams(params).toString();
        const response = await axios.get(`${API_BASE_URL}/api/products/find-all?${query}`);
        return response.data;
    },

    getProductById: async (id) => {
        try {
            const response = await axios.get(`${API_BASE_URL}/api/products/${id}`);
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    getFeaturedProducts: async () => {
        try {
            const response = await api.get(API_ENDPOINTS.FEATURED_PRODUCTS);
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    getProductsByCategory: async (categoryId, page = 1, limit = 8) => {
        try {
            const pageNumber = Number(page) || 1;
            const response = await axios.get(`${API_BASE_URL}/api/products/find-all?page=${pageNumber}&limit=${limit}&categoryId=${categoryId}`);
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    // Categories
    getCategories: async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/api/categories`);
            return response.data;
        } catch (error) {
            console.error('Error fetching categories:', error);
            throw error;
        }
    },

    // Cart
    getCart: async () => {
        try {
            const response = await api.get(API_ENDPOINTS.CART);
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    getCartByUser: async (userId) => {
        const response = await axios.get(`${API_BASE_URL}/api/carts/user/${userId}`);
        return response.data;
    },

    addToCart: async (cartData) => {
        // cartData: { productId, quantity, price, userId }
        const response = await axios.post(`${API_BASE_URL}/api/carts/create`, cartData);
        return response.data;
    },

    updateCartItem: async (id, quantity) => {
        try {
            const response = await axios.put(`${API_BASE_URL}/cart/update${id}`, { quantity });
            return response.data;
        } catch (error) { throw error; }
    },

    deleteCartItem: async (id) => {
        try {
            const response = await axios.delete(`${API_BASE_URL}/cart/delete/${id}`);
            return response.data;
        } catch (error) { throw error; }
    },

    clearCart: async (userId) => {
        try {
            const response = await axios.delete(`${API_BASE_URL}/cart/delete-all/${userId}`);
            return response.data;
        } catch (error) { throw error; }
    },

    // Orders
    getOrders: async (userId, page = 1, limit = 5) => {
        try {
            const response = await axios.get(`${API_BASE_URL}/orders/find-all?userId=${userId}&page=${page}&limit=${limit}`);
            return response.data;
        } catch (error) { throw error; }
    },

    getOrderById: async (id) => {
        try {
            const response = await axios.get(`${API_BASE_URL}/orders/${id}`);
            return response.data;
        } catch (error) { throw error; }
    },

    createOrder: async (orderRequest) => {
        try {
            const response = await axios.post(`${API_BASE_URL}/orders/create`, orderRequest);
            return response.data;
        } catch (error) { throw error; }
    },

    updateOrderStatus: async (id, status) => {
        try {
            const response = await axios.put(`${API_BASE_URL}/orders/change-status/${id}`, { status });
            return response.data;
        } catch (error) { throw error; }
    },

    // User
    getUserProfile: async () => {
        try {
            const response = await api.get(API_ENDPOINTS.USER_PROFILE);
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    updateUserProfile: async (userData) => {
        try {
            const response = await api.put(API_ENDPOINTS.UPDATE_PROFILE, userData);
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    // Blog
    getBlogs: async (params = {}) => {
        // params: { keyword, page, limit }
        const query = new URLSearchParams(params).toString();
        const response = await axios.get(`${API_BASE_URL}/api/blogs/find-all?${query}`);
        return response.data;
    },

    getBlogPostById: async (id) => {
        try {
            const response = await api.get(API_ENDPOINTS.BLOG_POST_DETAIL + id);
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    getFeaturedBlogPosts: async () => {
        try {
            const response = await api.get(API_ENDPOINTS.FEATURED_BLOG_POSTS);
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    getBlogById: async (id) => {
        try {
            const response = await axios.get(`${API_BASE_URL}/blogs/${id}`);
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    // Location
    getProvinces: async () => {
        try {
            const response = await axios.get(API_ENDPOINTS.PROVINCES + '?page=0&size=100');
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    getDistricts: async (provinceId) => {
        try {
            const response = await axios.get(API_ENDPOINTS.DISTRICTS + provinceId + '?page=0&size=100');
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    getWards: async (districtId) => {
        try {
            const response = await axios.get(API_ENDPOINTS.WARDS + districtId + '?page=0&size=100');
            return response.data;
        } catch (error) {
            throw error;
        }
    }
};

// Cart API
const CartAPI = {
    getByUserId: async (userId) => {
        try {
            const response = await axios.get(`${API_BASE_URL}/api/carts/user/${userId}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching cart:', error);
            throw error;
        }
    },

    addItem: async (cartData) => {
        try {
            const response = await axios.post(`${API_BASE_URL}/api/carts/create`, cartData);
            return response.data;
        } catch (error) {
            console.error('Error adding item to cart:', error);
            throw error;
        }
    },

    updateItem: async (id, quantity) => {
        try {
            const response = await axios.put(`${API_BASE_URL}/api/carts/update/${id}`, { quantity });
            return response.data;
        } catch (error) {
            console.error('Error updating cart item:', error);
            throw error;
        }
    },

    deleteItem: async (id) => {
        try {
            const response = await axios.delete(`${API_BASE_URL}/api/carts/delete/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting cart item:', error);
            throw error;
        }
    },

    clearCart: async (userId) => {
        try {
            const response = await axios.delete(`${API_BASE_URL}/api/carts/delete-all/${userId}`);
            return response.data;
        } catch (error) {
            console.error('Error clearing cart:', error);
            throw error;
        }
    }
};

// Order API
const OrderAPI = {
    getOrders: async (params = {}) => {
        try {
            const queryParams = new URLSearchParams();
            if (params.userId) queryParams.append('userId', params.userId);
            if (params.status) queryParams.append('status', params.status);
            if (params.keyword) queryParams.append('keyword', params.keyword);
            if (params.startDate) queryParams.append('startDate', params.startDate);
            if (params.endDate) queryParams.append('endDate', params.endDate);
            if (params.page) queryParams.append('page', params.page);
            if (params.limit) queryParams.append('limit', params.limit);

            const response = await axios.get(`${API_BASE_URL}/api/orders/find-all?${queryParams.toString()}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching orders:', error);
            throw error;
        }
    },

    createOrder: async (orderData) => {
        try {
            const response = await axios.post(`${API_BASE_URL}/api/orders/create`, orderData);
            return response.data;
        } catch (error) {
            console.error('Error creating order:', error);
            throw error;
        }
    },

    updateOrderStatus: async (id, status) => {
        try {
            const response = await axios.put(`${API_BASE_URL}/api/orders/change-status/${id}`, { status });
            return response.data;
        } catch (error) {
            console.error('Error updating order status:', error);
            throw error;
        }
    }
};
