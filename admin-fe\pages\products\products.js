document.addEventListener('DOMContentLoaded', function () {
  // Kiểm tra đăng nhập
  if (!checkAuth()) {
    return;
  }

  // Load layout components
  loadLayoutComponents();

  // Khởi tạo biến toàn cục
  let currentPage = 0;
  let pageSize = 10;
  let totalPages = 0;
  let productModal;
  let selectedThumbnail = null;
  let selectedVariants = [];

  // Load dữ liệu ban đầu
  loadCategories();
  loadSuppliers();
  loadProducts(currentPage, pageSize);

  // Khởi tạo modal sau khi DOM đã load
  setTimeout(() => {
    const modalElement = document.getElementById('productModal');
    if (modalElement) {
      productModal = new bootstrap.Modal(modalElement);
      // Xử lý sự kiện sau khi modal đã được khởi tạo
      setupEventListeners();
    }
  }, 500);

  // Hàm load layout components
  function loadLayoutComponents() {
    // Load header
    fetch('../../layout/header/header.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('header-container').innerHTML = html;

        // Load header script after HTML is inserted
        const headerScript = document.createElement('script');
        headerScript.src = '../../layout/header/header.js';
        document.body.appendChild(headerScript);
      });

    // Load sidebar
    fetch('../../layout/sidebar/sidebar.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('sidebar-container').innerHTML = html;

        // Load sidebar script after HTML is inserted
        const sidebarScript = document.createElement('script');
        sidebarScript.src = '../../layout/sidebar/sidebar.js';
        document.body.appendChild(sidebarScript);
      });

    // Load footer
    fetch('../../layout/footer/footer.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('footer-container').innerHTML = html;
      });
  }

  // Hàm load danh mục
  function loadCategories() {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    axios.get('http://localhost:8080/api/categories')
      .then(response => {
        const categories = response.data;
        const filterCategorySelect = document.getElementById('filterCategory');
        const productCategorySelect = document.getElementById('productCategory');

        // Xóa options cũ
        filterCategorySelect.innerHTML = '<option value="">Tất cả danh mục</option>';
        productCategorySelect.innerHTML = '<option value="">Chọn danh mục</option>';

        // Thêm options mới - chỉ có danh mục cấp 1
        categories.forEach(category => {
          const filterOption = document.createElement('option');
          filterOption.value = category.id;
          filterOption.textContent = category.name;
          filterCategorySelect.appendChild(filterOption);

          const productOption = document.createElement('option');
          productOption.value = category.id;
          productOption.textContent = category.name;
          productCategorySelect.appendChild(productOption);
        });
      })
      .catch(error => {
        handleApiError(error);
      });
  }

  // Hàm load nhà cung cấp
  function loadSuppliers() {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    axios.get('http://localhost:8080/api/suppliers')
      .then(response => {
        const suppliers = response.data;
        const filterSupplierSelect = document.getElementById('filterSupplier');
        const productSupplierSelect = document.getElementById('productSupplier');

        // Xóa options cũ
        filterSupplierSelect.innerHTML = '<option value="">Tất cả nhà cung cấp</option>';
        productSupplierSelect.innerHTML = '<option value="">Chọn nhà cung cấp</option>';

        // Thêm options mới
        suppliers.forEach(supplier => {
          const filterOption = document.createElement('option');
          filterOption.value = supplier.id;
          filterOption.textContent = supplier.name;
          filterSupplierSelect.appendChild(filterOption);

          const productOption = document.createElement('option');
          productOption.value = supplier.id;
          productOption.textContent = supplier.name;
          productSupplierSelect.appendChild(productOption);
        });
      })
      .catch(error => {
        handleApiError(error);
      });
  }

  //-----
  // Hàm load sản phẩm
  function loadProducts(page, size, categoryId = '', supplierId = '', status = '', keyword = '') {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    // Hiển thị loading
    document.getElementById('productTable').innerHTML = `
      <tr>
        <td colspan="9" class="text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Đang tải...</span>
          </div>
        </td>
      </tr>
    `;

    // Xây dựng URL với các tham số lọc
    let url = `http://localhost:8080/api/products/find-all?page=${page + 1}&limit=${size}`;

    if (keyword) {
      url += `&keyword=${encodeURIComponent(keyword)}`;
    } else {
      if (categoryId) {
        url += `&categoryId=${categoryId}`;
      } else if (supplierId) {
        url += `&supplierId=${supplierId}`;
      }
    }

    console.log(">>>>>>>>>>>> url = ", url);


    axios.get(url)
      .then(response => {
        let products;
        let totalElements;

        // Kiểm tra cấu trúc phản hồi
        if (response.data.content) {
          // Phản hồi dạng Page
          products = response.data.content;
          totalElements = response.data.totalElements;
          totalPages = response.data.totalPages;
        } else {
          // Phản hồi dạng List
          products = response.data;
          totalElements = products.length;
          totalPages = 1;
        }

        // Không cần lọc theo trạng thái nữa vì chỉ dùng xóa mềm

        // Hiển thị tổng số sản phẩm
        document.getElementById('totalProducts').textContent = totalElements;

        // Hiển thị danh sách sản phẩm
        displayProducts(products);

        // Hiển thị phân trang
        displayPagination(page, totalPages);
      })
      .catch(error => {
        handleApiError(error);

        // Hiển thị thông báo lỗi trong bảng
        document.getElementById('productTable').innerHTML = `
          <tr>
            <td colspan="9" class="text-center text-danger">
              Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại sau.
            </td>
          </tr>
        `;
      });
  }

  // Hàm hiển thị danh sách sản phẩm
  function displayProducts(products) {
    const tableBody = document.getElementById('productTable');
    tableBody.innerHTML = '';

    if (products.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="9" class="text-center">
            Không có sản phẩm nào
          </td>
        </tr>
      `;
      return;
    }

    products.forEach((product, index) => {
      const row = document.createElement('tr');

      // Không cần hiển thị trạng thái nữa vì chỉ dùng xóa mềm
      const statusBadge = '<span class="badge badge-active">Đang bán</span>';

      // Lấy danh sách màu sắc từ các biến thể
      let colorsList = 'N/A';
      if (product.variants && product.variants.length > 0) {
        const colors = product.variants.map(variant => variant.color);
        colorsList = colors.join(', ');
      } else if (product.colors && product.colors.length > 0) {
        // Hỗ trợ ngược dành cho dữ liệu cũ
        colorsList = product.colors.join(', ');
      }

      row.innerHTML = `
        <td>${index + 1 + currentPage * pageSize}</td>
        <td>
          <img src="${product.image ? 'http://localhost:8080/' + product.image : '../../assets/images/default-image.avif'}" 
            alt="${product.name}" class="img-thumbnail">
        </td>
        <td>
          <strong>${product.name}</strong>
          <br>
          
        </td>
        <td>${product.categoryName ? product.categoryName : 'N/A'}</td>
        <td>${formatCurrency(product.price)}</td>
        <td>${product.quantity || 0}</td>
        <td>
          <button class="btn btn-sm btn-info me-1 btn-edit" data-id="${product.id}">
            <i class="bi bi-pencil"></i>
          </button>
          <button class="btn btn-sm btn-danger btn-delete" data-id="${product.id}">
            <i class="bi bi-trash"></i>
          </button>
        </td>
      `;

      tableBody.appendChild(row);
    });

    // Thêm sự kiện cho các nút
    document.querySelectorAll('.btn-edit').forEach(button => {
      button.addEventListener('click', function () {
        const productId = this.getAttribute('data-id');
        editProduct(productId);
      });
    });

    document.querySelectorAll('.btn-delete').forEach(button => {
      button.addEventListener('click', function () {
        const productId = this.getAttribute('data-id');
        deleteProduct(productId);
      });
    });
  }

  // Hàm hiển thị phân trang
  function displayPagination(currentPage, totalPages) {
    const paginationContainer = document.getElementById('pagination');
    paginationContainer.innerHTML = '';

    if (totalPages <= 1) {
      return;
    }

    const pagination = createPagination(currentPage, totalPages, (page) => {
      currentPage = page;
      loadProducts(
        page,
        pageSize,
        document.getElementById('filterCategory').value,
        document.getElementById('filterSupplier').value,
        document.getElementById('filterStatus').value,
        document.getElementById('searchProduct').value
      );
    });

    paginationContainer.appendChild(pagination);
  }

  // Hàm thiết lập các sự kiện
  function setupEventListeners() {
    // Nút thêm sản phẩm
    const btnAddProduct = document.getElementById('btnAddProduct');
    if (btnAddProduct) {
      btnAddProduct.addEventListener('click', () => {
        resetProductForm();
        const modalLabel = document.getElementById('productModalLabel');
        if (modalLabel) modalLabel.textContent = 'Thêm sản phẩm mới';
        if (productModal) productModal.show();
      });
    }

    // Nút tìm kiếm
    const btnSearch = document.getElementById('btnSearch');
    if (btnSearch) {
      btnSearch.addEventListener('click', () => {
        const keyword = document.getElementById('searchProduct').value;
        currentPage = 0;
        loadProducts(currentPage, pageSize, '', '', '', keyword);
      });
    }

    // Bộ lọc danh mục
    const filterCategory = document.getElementById('filterCategory');
    if (filterCategory) {
      filterCategory.addEventListener('change', () => {
        const categoryId = filterCategory.value;
        currentPage = 0;
        loadProducts(currentPage, pageSize, categoryId, '', '', '');
      });
    }

    // Bộ lọc nhà cung cấp
    const filterSupplier = document.getElementById('filterSupplier');
    if (filterSupplier) {
      filterSupplier.addEventListener('change', () => {
        const supplierId = filterSupplier.value;
        currentPage = 0;
        loadProducts(currentPage, pageSize, '', supplierId, '', '');
      });
    }

    // Nút lưu sản phẩm
    const btnSaveProduct = document.getElementById('btnSaveProduct');
    if (btnSaveProduct) {
      btnSaveProduct.addEventListener('click', saveProduct);
    }

    // Xử lý input tìm kiếm
    const searchProduct = document.getElementById('searchProduct');
    if (searchProduct) {
      searchProduct.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          const keyword = searchProduct.value;
          currentPage = 0;
          loadProducts(currentPage, pageSize, '', '', '', keyword);
        }
      });
    }

    // Xử lý upload ảnh
    const productThumbnail = document.getElementById('productThumbnail');
    if (productThumbnail) {
      productThumbnail.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            selectedThumbnail = e.target.result;
            const previewContainer = document.getElementById('thumbnailPreview');
            previewContainer.innerHTML = `
              <img src="${e.target.result}" class="img-thumbnail" style="max-height: 200px;">
            `;
          };
          reader.readAsDataURL(file);
        }
      });
    }
  }

  // Hàm thêm thẻ màu sắc
  function addColorTag() {
    const colorInput = document.getElementById('productColorInput');
    const color = colorInput.value.trim();

    if (color && !selectedColors.includes(color)) {
      selectedColors.push(color);
      renderColorTags();
      colorInput.value = '';
    }

    colorInput.focus();
  }

  // Hàm hiển thị các thẻ màu sắc
  function renderColorTags() {
    const container = document.getElementById('productColorsContainer');
    container.innerHTML = '';

    selectedColors.forEach((color, index) => {
      const tag = document.createElement('span');
      tag.className = 'color-tag';
      tag.innerHTML = `${color} <span class="remove-color" data-index="${index}">×</span>`;
      container.appendChild(tag);

      // Thêm sự kiện xóa màu
      tag.querySelector('.remove-color').addEventListener('click', function () {
        const idx = parseInt(this.getAttribute('data-index'));
        selectedColors.splice(idx, 1);
        renderColorTags();
      });
    });
  }

  // Hàm reset form
  function resetProductForm() {
    const form = document.getElementById('productForm');
    if (!form) return;

    form.reset();
    
    const productId = document.getElementById('productId');
    if (productId) productId.value = '';
    
    const thumbnailPreview = document.getElementById('thumbnailPreview');
    if (thumbnailPreview) thumbnailPreview.innerHTML = '';
    
    const variantsContainer = document.getElementById('variantsContainer');
    if (variantsContainer) variantsContainer.innerHTML = '';
    
    selectedThumbnail = null;
    selectedVariants = [];
  }

  // Hàm lưu sản phẩm
  async function saveProduct() {
    const productId = document.getElementById('productId').value;
    const name = document.getElementById('productName').value;
    const description = document.getElementById('productDescription').value;
    const quantity = parseInt(document.getElementById('productQuantity').value) || 0;
    const price = parseFloat(document.getElementById('productPrice').value) || 0;
    const categoryId = document.getElementById('productCategory').value;
    const supplierId = document.getElementById('productSupplier').value;

    if (!name || !categoryId) {
      Swal.fire({
        icon: 'error',
        title: 'Lỗi',
        text: 'Vui lòng điền đầy đủ thông tin bắt buộc'
      });
      return;
    }

    let image = null;
    const productThumbnail = document.getElementById('productThumbnail');
    if (productThumbnail && productThumbnail.files && productThumbnail.files[0]) {
      // Có chọn file mới, upload lên server
      const formData = new FormData();
      formData.append('file', productThumbnail.files[0]);
      try {
        const uploadRes = await axios.post('http://localhost:8080/files/upload', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
        image = uploadRes.data;
      } catch (err) {
        Swal.fire({ icon: 'error', title: 'Lỗi', text: 'Upload ảnh thất bại!' });
        return;
      }
    } else if (selectedThumbnail) {
      // Nếu không chọn file mới nhưng đã có ảnh cũ (edit)
      image = selectedThumbnail;
    }

    const productData = {
      name,
      description,
      quantity,
      price,
      categoryId: parseInt(categoryId),
      supplierId: supplierId ? parseInt(supplierId) : null,
      image: image
    };

    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    const url = productId
      ? `http://localhost:8080/api/products/update/${productId}`
      : 'http://localhost:8080/api/products/create';

    const method = productId ? 'put' : 'post';

    axios[method](url, productData)
      .then(response => {
        Swal.fire({
          icon: 'success',
          title: 'Thành công',
          text: productId ? 'Cập nhật sản phẩm thành công' : 'Thêm sản phẩm thành công'
        }).then(() => {
          productModal.hide();
          loadProducts(currentPage, pageSize);
        });
      })
      .catch(error => {
        handleApiError(error);
      });
  }

  // Hàm sửa sản phẩm
  function editProduct(productId) {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    axios.get(`http://localhost:8080/api/products/${productId}`)
      .then(response => {
        const product = response.data;

        // Điền dữ liệu vào form (chỉ các trường còn tồn tại)
        const productIdInput = document.getElementById('productId');
        if (productIdInput) productIdInput.value = product.id;
        const productName = document.getElementById('productName');
        if (productName) productName.value = product.name;
        const productCategory = document.getElementById('productCategory');
        if (productCategory) productCategory.value = product.category ? product.category.id : '';
        const productSupplier = document.getElementById('productSupplier');
        if (productSupplier) productSupplier.value = product.supplier ? product.supplier.id : '';
        const productPrice = document.getElementById('productPrice');
        if (productPrice) productPrice.value = product.price;
        const productQuantity = document.getElementById('productQuantity');
        if (productQuantity) productQuantity.value = product.quantity || 0;
        const productDescription = document.getElementById('productDescription');
        if (productDescription) productDescription.value = product.description || '';

        // Hiển thị ảnh đại diện (nếu có)
        selectedThumbnail = product.image || null;
        const thumbnailPreview = document.getElementById('thumbnailPreview');
        if (thumbnailPreview && product.image) {
          thumbnailPreview.innerHTML = `<img src="http://localhost:8080/${product.image}" class="img-thumbnail" style="max-height: 200px;">`;
        } else if (thumbnailPreview) {
          thumbnailPreview.innerHTML = '';
        }

        // Hiển thị modal
        const modalLabel = document.getElementById('productModalLabel');
        if (modalLabel) modalLabel.textContent = 'Cập nhật sản phẩm';
        if (productModal) productModal.show();
      })
      .catch(error => {
        handleApiError(error);
      });
  }

  // Hàm xóa sản phẩm
  function deleteProduct(productId) {
    showConfirmDialog(
      'Xác nhận xóa',
      'Bạn có chắc chắn muốn xóa sản phẩm này không?',
      function () {
        const token = localStorage.getItem('token');
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        axios.delete(`http://localhost:8080/api/products/delete/${productId}`)
          .then(response => {
            showSuccessToast('Xóa sản phẩm thành công');
            loadProducts(currentPage, pageSize);
          })
          .catch(error => {
            handleApiError(error);
          });
      }
    );
  }
});
