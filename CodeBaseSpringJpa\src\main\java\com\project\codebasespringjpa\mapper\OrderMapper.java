package com.project.codebasespringjpa.mapper;

import com.project.codebasespringjpa.dto.order.request.OrderRequest;
import com.project.codebasespringjpa.dto.order.response.OrderDetailResponse;
import com.project.codebasespringjpa.dto.order.response.OrderResponse;
import com.project.codebasespringjpa.entity.OrderEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.enums.OrderStatusEnum;
import com.project.codebasespringjpa.enums.PaymentStatusEnum;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderMapper {
    @Autowired
    OrderDetailMapper orderDetailMapper;

    public OrderEntity toEntity(OrderRequest request){
        UserEntity user = UserEntity.builder()
                .id(request.getUserId())
                .build();

        return OrderEntity.builder()
                .username(request.getUsername())
                .phone(request.getPhone())

                .totalAmount(request.getTotalAmount())
                .status(request.getStatus())
                .shippingAddress(request.getShippingAddress())

                .paymentMethod(request.getPaymentMethod())
                .paymentStatus(request.getPaymentStatus())

                .note(request.getNote())

                .user(user)
                .build();
    }

    public OrderResponse toResponse(OrderEntity entity){
        List<OrderDetailResponse> orderDetailResponses = new ArrayList<>();
        try {
            orderDetailResponses = entity.getOrderDetails().stream()
                    .map(it -> orderDetailMapper.toResponse(it)).toList();
        }catch (Exception e){
            log.error("Loi con vert order mapper: " + e);
        }

        return OrderResponse.builder()
                .id(entity.getId())

                .userId(entity.getUser().getId())
                .username(entity.getUsername())
                .phone(entity.getPhone())

                .orderDate(entity.getCreateDate())
                .totalAmount(entity.getTotalAmount())
                .status(entity.getStatus())
                .shippingAddress(entity.getShippingAddress())
                .paymentMethod(entity.getPaymentMethod())
                .paymentStatus(entity.getPaymentStatus())
                .note(entity.getNote())

                .orderDetails(orderDetailResponses)
                .build();
    }
}
