package com.project.codebasespringjpa.mapper;

import com.project.codebasespringjpa.dto.supplier.request.SupplierCreateRequest;
import com.project.codebasespringjpa.dto.supplier.request.SupplierUpdateRequest;
import com.project.codebasespringjpa.dto.supplier.response.SupplierResponse;
import com.project.codebasespringjpa.entity.SupplierEntity;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class SupplierMapper {
    
    /**
     * <PERSON>yển đổi từ SupplierEntity sang SupplierResponse
     */
    public SupplierResponse toResponse(SupplierEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return SupplierResponse.builder()
                .id(entity.getId())
                .name(entity.getName())
                .email(entity.getEmail())
                .phone(entity.getPhone())
                .address(entity.getAddress())
                .description(entity.getDescription())
                .status(entity.getStatus())
                .createDate(entity.getCreateDate())
                .createBy(entity.getCreateBy())
                .updateDate(entity.getUpdateDate())
                .updateBy(entity.getUpdateBy())
                .build();
    }
    
    /**
     * Chuyển đổi từ SupplierCreateRequest sang SupplierEntity
     */
    public SupplierEntity toEntity(SupplierCreateRequest request) {
        if (request == null) {
            return null;
        }
        
        return SupplierEntity.builder()
                .name(request.getName())
                .email(request.getEmail())
                .phone(request.getPhone())
                .address(request.getAddress())
                .description(request.getDescription())
                .status(request.getStatus() != null ? request.getStatus() : true)
                .build();
    }
    
    /**
     * Cập nhật SupplierEntity từ SupplierUpdateRequest
     */
    public void updateEntityFromRequest(SupplierEntity entity, SupplierUpdateRequest request) {
        if (entity == null || request == null) {
            return;
        }
        
        if (request.getName() != null) {
            entity.setName(request.getName());
        }
        
        if (request.getEmail() != null) {
            entity.setEmail(request.getEmail());
        }
        
        if (request.getPhone() != null) {
            entity.setPhone(request.getPhone());
        }
        
        if (request.getAddress() != null) {
            entity.setAddress(request.getAddress());
        }
        
        if (request.getDescription() != null) {
            entity.setDescription(request.getDescription());
        }
        
        if (request.getStatus() != null) {
            entity.setStatus(request.getStatus());
        }
    }
    
    /**
     * Chuyển đổi danh sách SupplierEntity sang danh sách SupplierResponse
     */
    public List<SupplierResponse> toResponseList(List<SupplierEntity> entities) {
        if (entities == null) {
            return new ArrayList<>();
        }
        
        return entities.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }
}
