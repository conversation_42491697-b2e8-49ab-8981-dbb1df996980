// Review Service for Client

// Review API
const ReviewService = {
    getByProductId: async (productId) => {
        return await apiRequest(`reviews/product/${productId}`);
    },
    
    getByUserId: async (userId) => {
        return await apiRequest(`reviews/user/${userId}`);
    },
    
    create: async (reviewData) => {
        return await apiRequest('reviews', 'POST', reviewData);
    },
    
    update: async (id, reviewData) => {
        return await apiRequest(`reviews/${id}`, 'PUT', reviewData);
    },
    
    delete: async (id) => {
        return await apiRequest(`reviews/${id}`, 'DELETE');
    }
};
