IDE: intellij 
đây là dự án jpa code first, nên thiếu trường, thiếu bảng thì hãy viết thêm entity
viết code theo dạng cấu trúc có trước của dự án 
Thêm seed data tại ApplicationInitConfig
với mỗi dữ liệu gửi về api, cần nhận 1 đố<PERSON> tượ<PERSON> json (n<PERSON><PERSON> <PERSON><PERSON>, sử dụng api g<PERSON><PERSON> ảnh, để lấy đường dẫn)
Thiếu thư viện mvn thì thêm vào pomx 
Nên chia request và response, chứ không dùng 1 DTO (class) (tôi đã có mẫu từ trước) 
Sử dụng mapper thủ công, các class mapper (component ) được định nghĩa trong 1 package mapper. mapper này không cần interface dùng chung 
Nếu muốn thêm exceptionj thì định nghĩa ở ErrorCode enum (tôi đã tạo trong dự án rồi), rồi ném exception ra (ví dụ: new AppException(ErrorCode....))