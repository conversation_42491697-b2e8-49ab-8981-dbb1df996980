document.addEventListener('DOMContentLoaded', function() {
  // Kiểm tra đăng nhập
  if (!checkAuth()) {
    return;
  }
  
  // Load layout components
  loadLayoutComponents();
  
  // Khởi tạo biến toàn cục
  let currentPage = 0;
  let pageSize = 10;
  let totalPages = 0;
  let supplierModal;
  
  // Khởi tạo modal
  supplierModal = new bootstrap.Modal(document.getElementById('supplierModal'));
  
  // Load dữ liệu ban đầu
  loadSuppliers(currentPage, pageSize);
  
  // Xử lý sự kiện
  setupEventListeners();
  
  // Hàm load layout components
  function loadLayoutComponents() {
    // Load header
    fetch('../../layout/header/header.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('header-container').innerHTML = html;
        
        // Load header script after HTML is inserted
        const headerScript = document.createElement('script');
        headerScript.src = '../../layout/header/header.js';
        document.body.appendChild(headerScript);
      });
    
    // Load sidebar
    fetch('../../layout/sidebar/sidebar.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('sidebar-container').innerHTML = html;
        
        // Load sidebar script after HTML is inserted
        const sidebarScript = document.createElement('script');
        sidebarScript.src = '../../layout/sidebar/sidebar.js';
        document.body.appendChild(sidebarScript);
      });
    
    // Load footer
    fetch('../../layout/footer/footer.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('footer-container').innerHTML = html;
      });
  }
  
  // Hàm load nhà cung cấp
  function loadSuppliers(page, size, status = '', keyword = '') {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    // Hiển thị loading
    document.getElementById('supplierTable').innerHTML = `
      <tr>
        <td colspan="7" class="text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Đang tải...</span>
          </div>
        </td>
      </tr>
    `;
    
    // Xây dựng URL với các tham số lọc
    let url = `http://localhost:8080/api/suppliers?page=${page}&size=${size}`;
    
    if (keyword) {
      // url = `http://localhost:8080/api/suppliers/search?keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`;
      url = `http://localhost:8080/api/suppliers?keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`;
    }
    
    axios.get(url)
      .then(response => {
        let suppliers;
        let totalElements;
        
        // Kiểm tra cấu trúc phản hồi
        if (response.data.content) {
          // Phản hồi dạng Page
          suppliers = response.data.content;
          totalElements = response.data.totalElements;
          totalPages = response.data.totalPages;
        } else {
          // Phản hồi dạng List
          suppliers = response.data;
          totalElements = suppliers.length;
          totalPages = 1;
        }
        
        // Lọc theo trạng thái nếu có
        if (status !== '') {
          suppliers = suppliers.filter(supplier => supplier.status.toString() === status);
          totalElements = suppliers.length;
        }
        
        // Hiển thị tổng số nhà cung cấp
        document.getElementById('totalSuppliers').textContent = totalElements;
        
        // Hiển thị danh sách nhà cung cấp
        displaySuppliers(suppliers);
        
        // Hiển thị phân trang
        displayPagination(page, totalPages);
      })
      .catch(error => {
        handleApiError(error);
        
        // Hiển thị thông báo lỗi trong bảng
        document.getElementById('supplierTable').innerHTML = `
          <tr>
            <td colspan="7" class="text-center text-danger">
              Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại sau.
            </td>
          </tr>
        `;
      });
  }
  
  // Hàm hiển thị danh sách nhà cung cấp
  function displaySuppliers(suppliers) {
    const tableBody = document.getElementById('supplierTable');
    tableBody.innerHTML = '';
    
    if (suppliers.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="7" class="text-center">
            Không có nhà cung cấp nào
          </td>
        </tr>
      `;
      return;
    }
    
    suppliers.forEach((supplier, index) => {
      const row = document.createElement('tr');
      
      // Tạo badge cho trạng thái
      const statusBadge = supplier.status 
        ? '<span class="badge badge-active">Hoạt động</span>' 
        : '<span class="badge badge-inactive">Ngừng hoạt động</span>';
      
      row.innerHTML = `
        <td>${index + 1 + currentPage * pageSize}</td>
        <td>${supplier.name}</td>
        <td>${supplier.email}</td>
        <td>${supplier.phone}</td>
        <td>${supplier.address || 'N/A'}</td>
        <td>${statusBadge}</td>
        <td>
          <button class="btn btn-sm btn-info me-1 btn-edit" data-id="${supplier.id}">
            <i class="bi bi-pencil"></i>
          </button>
          <button class="btn btn-sm btn-danger btn-delete" data-id="${supplier.id}">
            <i class="bi bi-trash"></i>
          </button>
        </td>
      `;
      
      tableBody.appendChild(row);
    });
    
    // Thêm sự kiện cho các nút
    document.querySelectorAll('.btn-edit').forEach(button => {
      button.addEventListener('click', function() {
        const supplierId = this.getAttribute('data-id');
        editSupplier(supplierId);
      });
    });
    
    document.querySelectorAll('.btn-delete').forEach(button => {
      button.addEventListener('click', function() {
        const supplierId = this.getAttribute('data-id');
        deleteSupplier(supplierId);
      });
    });
  }
  
  // Hàm hiển thị phân trang
  function displayPagination(currentPage, totalPages) {
    const paginationContainer = document.getElementById('pagination');
    paginationContainer.innerHTML = '';
    
    if (totalPages <= 1) {
      return;
    }
    
    const pagination = createPagination(currentPage, totalPages, (page) => {
      currentPage = page;
      loadSuppliers(
        page, 
        pageSize, 
        document.getElementById('filterStatus').value,
        document.getElementById('searchSupplier').value
      );
    });
    
    paginationContainer.appendChild(pagination);
  }
  
  // Hàm thiết lập các sự kiện
  function setupEventListeners() {
    // Sự kiện nút thêm nhà cung cấp
    document.getElementById('btnAddSupplier').addEventListener('click', function() {
      resetSupplierForm();
      document.getElementById('supplierModalLabel').textContent = 'Thêm nhà cung cấp mới';
      supplierModal.show();
    });
    
    // Sự kiện nút lưu nhà cung cấp
    document.getElementById('btnSaveSupplier').addEventListener('click', saveSupplier);
    
    // Sự kiện nút tìm kiếm
    document.getElementById('btnSearch').addEventListener('click', function() {
      currentPage = 0;
      loadSuppliers(
        currentPage, 
        pageSize, 
        document.getElementById('filterStatus').value,
        document.getElementById('searchSupplier').value
      );
    });
    
    // Sự kiện thay đổi bộ lọc
    document.getElementById('filterStatus').addEventListener('change', function() {
      currentPage = 0;
      loadSuppliers(
        currentPage, 
        pageSize, 
        this.value,
        document.getElementById('searchSupplier').value
      );
    });
  }
  
  // Hàm reset form nhà cung cấp
  function resetSupplierForm() {
    document.getElementById('supplierForm').reset();
    document.getElementById('supplierId').value = '';
  }
  
  // Hàm lưu nhà cung cấp
  function saveSupplier() {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    // Lấy dữ liệu từ form
    const supplierId = document.getElementById('supplierId').value;
    const name = document.getElementById('supplierName').value;
    const email = document.getElementById('supplierEmail').value;
    const phone = document.getElementById('supplierPhone').value;
    const address = document.getElementById('supplierAddress').value;
    const status = document.getElementById('supplierStatus').checked;
    
    // Kiểm tra dữ liệu bắt buộc
    if (!name || !email || !phone) {
      showErrorToast('Vui lòng điền đầy đủ thông tin bắt buộc');
      return;
    }
    
    // Tạo đối tượng dữ liệu
    const supplierData = {
      name,
      email,
      phone,
      address,
      status
    };
    
    // Gọi API tạo/cập nhật nhà cung cấp
    if (supplierId) {
      // Cập nhật nhà cung cấp
      axios.put(`http://localhost:8080/api/suppliers/${supplierId}`, supplierData)
        .then(response => {
          showSuccessToast('Cập nhật nhà cung cấp thành công');
          supplierModal.hide();
          loadSuppliers(currentPage, pageSize);
        })
        .catch(error => {
          handleApiError(error);
        });
    } else {
      // Tạo nhà cung cấp mới
      axios.post('http://localhost:8080/api/suppliers', supplierData)
        .then(response => {
          showSuccessToast('Thêm nhà cung cấp thành công');
          supplierModal.hide();
          loadSuppliers(0, pageSize);
        })
        .catch(error => {
          handleApiError(error);
        });
    }
  }
  
  // Hàm sửa nhà cung cấp
  function editSupplier(supplierId) {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    axios.get(`http://localhost:8080/api/suppliers/${supplierId}`)
      .then(response => {
        const supplier = response.data;
        
        // Điền dữ liệu vào form
        document.getElementById('supplierId').value = supplier.id;
        document.getElementById('supplierName').value = supplier.name;
        document.getElementById('supplierEmail').value = supplier.email;
        document.getElementById('supplierPhone').value = supplier.phone;
        document.getElementById('supplierAddress').value = supplier.address || '';
        document.getElementById('supplierStatus').checked = supplier.status;
        
        // Hiển thị modal
        document.getElementById('supplierModalLabel').textContent = 'Cập nhật nhà cung cấp';
        supplierModal.show();
      })
      .catch(error => {
        handleApiError(error);
      });
  }
  
  // Hàm xóa nhà cung cấp
  function deleteSupplier(supplierId) {
    showConfirmDialog(
      'Xác nhận xóa',
      'Bạn có chắc chắn muốn xóa nhà cung cấp này không?',
      function() {
        const token = localStorage.getItem('token');
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        
        axios.delete(`http://localhost:8080/api/suppliers/${supplierId}`)
          .then(response => {
            showSuccessToast('Xóa nhà cung cấp thành công');
            loadSuppliers(currentPage, pageSize);
          })
          .catch(error => {
            if (error.response && error.response.status === 400) {
              showErrorToast('Không thể xóa nhà cung cấp này vì đang có sản phẩm liên kết');
            } else {
              handleApiError(error);
            }
          });
      }
    );
  }
});
