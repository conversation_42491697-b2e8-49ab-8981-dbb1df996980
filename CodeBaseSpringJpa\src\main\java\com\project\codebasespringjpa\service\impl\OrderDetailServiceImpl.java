package com.project.codebasespringjpa.service.impl;

import com.project.codebasespringjpa.dto.order.request.OrderDetailRequest;
import com.project.codebasespringjpa.dto.order.response.OrderDetailResponse;
import com.project.codebasespringjpa.entity.OrderDetailEntity;
import com.project.codebasespringjpa.entity.OrderEntity;
import com.project.codebasespringjpa.entity.ProductEntity;
import com.project.codebasespringjpa.exception.AppException;
import com.project.codebasespringjpa.exception.ErrorCode;
import com.project.codebasespringjpa.mapper.OrderDetailMapper;
import com.project.codebasespringjpa.repository.IOrderDetailRepository;
import com.project.codebasespringjpa.repository.IOrderRepository;
import com.project.codebasespringjpa.repository.IProductRepository;
import com.project.codebasespringjpa.service.interfaces.IOrderDetailService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class OrderDetailServiceImpl implements IOrderDetailService {
    IOrderDetailRepository orderDetailRepository;
    IOrderRepository orderRepository;
    IProductRepository productRepository;
    OrderDetailMapper orderDetailMapper;

    @Override
    public List<OrderDetailResponse> findAll() {
        List<OrderDetailEntity> orderDetails = orderDetailRepository.findAll();
        return orderDetails.stream().map(it -> orderDetailMapper.toResponse(it)).toList();
    }

    @Override
    public OrderDetailResponse findById(Long id) {
        OrderDetailEntity orderDetail = orderDetailRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.ORDER_ITEM_NOT_FOUND));
        return orderDetailMapper.toResponse(orderDetail);
    }

    @Override
    public List<OrderDetailResponse> findByOrderId(Long orderId) {
        OrderEntity order = orderRepository.findById(orderId)
                .orElseThrow(() -> new AppException(ErrorCode.ORDER_NOT_FOUND));

        List<OrderDetailEntity> orderDetails = orderDetailRepository.findByOrder(order);
        return orderDetails.stream().map(it -> orderDetailMapper.toResponse(it)).toList();
    }

    @Override
    public List<OrderDetailResponse> findByProductId(Long productId) {
        ProductEntity product = productRepository.findById(productId)
                .orElseThrow(() -> new AppException(ErrorCode.PRODUCT_NOT_FOUND));

        List<OrderDetailEntity> orderDetails = orderDetailRepository.findByProduct(product);
        return orderDetails.stream().map(it -> orderDetailMapper.toResponse(it)).toList();
    }

    @Override
    public List<Map<String, Object>> findTopSellingProducts() {
        List<Object[]> results = orderDetailRepository.findTopSellingProducts();
        List<Map<String, Object>> mappedResults = new ArrayList<>();

        for (Object[] result : results) {
            Map<String, Object> map = new HashMap<>();
            map.put("productId", result[0]);
            map.put("totalQuantity", result[1]);
            mappedResults.add(map);
        }

        return mappedResults;
    }

    @Override
    public OrderDetailResponse create(Long orderId, OrderDetailRequest request) {
        OrderEntity order = orderRepository.findById(orderId)
                .orElseThrow(() -> new AppException(ErrorCode.ORDER_NOT_FOUND));

        ProductEntity product = productRepository.findById(request.getProductId())
                .orElseThrow(() -> new AppException(ErrorCode.PRODUCT_NOT_FOUND));


        // Tính giá và tổng tiền


        // Cập nhật tổng tiền đơn hàng

return null;
    }

    @Override
    public void delete(Long id) {
        OrderDetailEntity orderDetail = orderDetailRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.ORDER_ITEM_NOT_FOUND));

        OrderEntity order = orderDetail.getOrder();

        orderDetailRepository.delete(orderDetail);

        // Cập nhật tổng tiền đơn hàng
    }


}
