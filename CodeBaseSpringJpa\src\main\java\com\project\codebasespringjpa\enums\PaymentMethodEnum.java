package com.project.codebasespringjpa.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum PaymentMethodEnum {
    CASH_ON_DELIVERY,
    BANK_TRANSFER,
    CREDIT_CARD,
    PAYPAL,
    MOMO;

    public static List<String> methodList() {
        List<String> methodList = Arrays.stream(PaymentMethodEnum.values())
                .map(Enum::name)
                .collect(Collectors.toList());

        return methodList;
    }
}
