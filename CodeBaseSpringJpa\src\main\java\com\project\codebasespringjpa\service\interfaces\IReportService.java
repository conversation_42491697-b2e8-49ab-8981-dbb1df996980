package com.project.codebasespringjpa.service.interfaces;

import com.project.codebasespringjpa.dto.report.InventoryReportResponse;
import com.project.codebasespringjpa.dto.report.SalesReportResponse;

import java.time.LocalDate;

public interface IReportService {
    SalesReportResponse getSalesReport(String period, LocalDate dateFrom, LocalDate dateTo);
    
    InventoryReportResponse getInventoryReport(Long categoryId, Long supplierId, String stockStatus, int page, int size);
    
    byte[] exportSalesReport(String period, LocalDate dateFrom, LocalDate dateTo, String type);
    
    byte[] exportInventoryReport(Long categoryId, Long supplierId, String stockStatus, String type);
}
