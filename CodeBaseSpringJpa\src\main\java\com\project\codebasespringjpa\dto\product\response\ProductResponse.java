package com.project.codebasespringjpa.dto.product.response;

import com.project.codebasespringjpa.dto.category.response.CategoryResponse;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProductResponse {
    Long id;
    String name;
    String description;
    Integer quantity;
    Double price;
    String image;
    Long categoryId;
    String categoryName;
    Long supplierId;
    String supplierName;
}
