package com.project.codebasespringjpa.mapper;

import com.project.codebasespringjpa.dto.blog.request.BlogRequest;
import com.project.codebasespringjpa.dto.blog.response.BlogResponse;
import com.project.codebasespringjpa.entity.BlogEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.util.ImageUtil;
import com.project.codebasespringjpa.util.UtilVariable;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class BlogMapper {
    public BlogEntity toEntity(BlogRequest request){
        return BlogEntity.builder()
                .title(request.getTitle())
                .content(request.getContent())
                .image(request.getImage())
                .build();
    }

    public BlogResponse toResponse(BlogEntity entity){
        String image = entity.getImage();
        if(image == null)
            image = UtilVariable.IMAGE_DEFAULT;
        return BlogResponse.builder()
                .id(entity.getId())
                .title(entity.getTitle())
                .content(entity.getContent())
                .image(image)
                .createDate(entity.getCreateDate())
                .build();
    }
}
