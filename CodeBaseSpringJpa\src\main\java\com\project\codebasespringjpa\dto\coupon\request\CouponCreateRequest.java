package com.project.codebasespringjpa.dto.coupon.request;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CouponCreateRequest {
    String code;
    String discountType;
    BigDecimal discountValue;
    BigDecimal minOrderAmount;
    LocalDateTime startDate;
    LocalDateTime endDate;
    Integer usageLimit;
    Boolean status;
}
