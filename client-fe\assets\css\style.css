/* Global Styles */
:root {
    --primary-color: #ff6b6b;
    --secondary-color: #546de5;
    --accent-color: #f7d794;
    --text-color: #333;
    --light-gray: #f8f9fa;
    --dark-gray: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    line-height: 1.6;
}

a {
    text-decoration: none;
    color: var(--secondary-color);
    transition: all 0.3s ease;
}

a:hover {
    color: var(--primary-color);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #ff5252;
    border-color: #ff5252;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Header Styles */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.nav-link {
    font-weight: 500;
}

.nav-link.active {
    color: var(--primary-color) !important;
}

.search-form {
    position: relative;
}

.search-form .form-control {
    padding-right: 40px;
    border-radius: 20px;
}

.search-form .btn {
    position: absolute;
    right: 5px;
    top: 5px;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Banner Styles */
.banner-container {
    margin-bottom: 3rem;
}

.carousel-item img {
    height: 500px;
    object-fit: cover;
}

.carousel-caption {
    background: rgba(0, 0, 0, 0.5);
    padding: 20px;
    border-radius: 10px;
}

/* Category Cards */
.category-card {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.category-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    padding: 20px;
    color: white;
    text-align: center;
}

.category-overlay h3 {
    margin-bottom: 10px;
    font-size: 1.5rem;
}

/* Product Cards */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
}

.card-title {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    height: 2.4rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* Blog Post Cards */
.blog-card {
    height: 100%;
}

.blog-card .card-img-top {
    height: 200px;
    object-fit: cover;
}

.blog-meta {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.blog-meta i {
    margin-right: 5px;
}

.blog-excerpt {
    font-size: 0.95rem;
    color: #666;
    margin-bottom: 1rem;
    height: 3rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* Footer Styles */
footer {
    background-color: var(--dark-gray);
    color: white;
    padding: 50px 0 20px;
}

.footer-title {
    font-weight: 600;
    margin-bottom: 20px;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #adb5bd;
}

.footer-links a:hover {
    color: white;
}

.social-links {
    font-size: 1.5rem;
}

.social-links a {
    color: white;
    margin-right: 15px;
}

.social-links a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    margin-top: 30px;
}

/* Form Styles */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 107, 107, 0.25);
}

/* Authentication Pages */
.auth-container {
    max-width: 500px;
    margin: 50px auto;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Cart Styles */
.cart-item {
    border-bottom: 1px solid #eee;
    padding: 15px 0;
}

.cart-item-img {
    width: 100px;
    height: 100px;
    object-fit: cover;
}

.quantity-control {
    display: flex;
    align-items: center;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #eee;
    border: none;
    cursor: pointer;
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: 1px solid #eee;
    margin: 0 10px;
}

/* Product Detail Page */
.product-detail-img {
    width: 100%;
    border-radius: 10px;
}

.product-detail-info {
    padding: 20px;
}

.product-detail-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.product-detail-price {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.product-detail-rating {
    color: #ffc107;
    margin-bottom: 15px;
}

.product-detail-description {
    margin-bottom: 20px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .carousel-item img {
        height: 300px;
    }
    
    .category-card img {
        height: 200px;
    }
    
    .card-img-top {
        height: 180px;
    }
}

@media (max-width: 576px) {
    .carousel-item img {
        height: 200px;
    }
    
    .category-card img {
        height: 150px;
    }
    
    .card-img-top {
        height: 160px;
    }
}
