package com.project.codebasespringjpa.service.impl;

import com.project.codebasespringjpa.entity.RoleEntity;
import com.project.codebasespringjpa.repository.IRoleRepository;
import com.project.codebasespringjpa.service.interfaces.IRoleService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RoleServiceImpl implements IRoleService {
    IRoleRepository roleRepository;

    @Override
    public boolean exitsByName(String name) {
        return roleRepository.existsByName(name);
    }

    @Override
    public void create(String name) {
        if (!exitsByName(name)) {
            RoleEntity role = RoleEntity.builder()
                    .name(name)
                    .build();
            roleRepository.save(role);
        }
    }
}
