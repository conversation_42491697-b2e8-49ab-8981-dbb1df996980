package com.project.codebasespringjpa.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.project.codebasespringjpa.dto.product.request.ProductRequest;
import com.project.codebasespringjpa.dto.product.response.ProductResponse;
import com.project.codebasespringjpa.entity.CategoryEntity;
import com.project.codebasespringjpa.entity.ProductEntity;
import com.project.codebasespringjpa.entity.SupplierEntity;
import com.project.codebasespringjpa.util.UtilFile;
import com.project.codebasespringjpa.util.UtilVariable;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ProductMapper {

    private final ObjectMapper objectMapper;
    private final CategoryMapper categoryMapper;
    private final SupplierMapper supplierMapper;

    public ProductResponse toResponse(ProductEntity entity) {
        String image = entity.getImage();
        if(image == null)
            image = UtilVariable.IMAGE_DEFAULT;

        return ProductResponse.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .quantity(entity.getQuantity())
                .price(entity.getPrice())
                .image(image)
                .categoryId(entity.getCategory().getId())
                .categoryName(entity.getCategory().getName())
                .supplierId(entity.getSupplier().getId())
                .supplierName(entity.getSupplier().getName())
                .build();
    }

    public ProductEntity toEntity(ProductRequest request) {
        SupplierEntity supplierEntity = SupplierEntity.builder()
                .id(request.getSupplierId())
                .build();

        CategoryEntity categoryEntity = CategoryEntity.builder()
                .id(request.getCategoryId())
                .build();

        return ProductEntity.builder()
                .name(request.getName())
                .description(request.getDescription())
                .quantity(request.getQuantity())
                .price(request.getPrice())
                .image(request.getImage())
                .supplier(supplierEntity)
                .category(categoryEntity)
                .build();
    }


}
