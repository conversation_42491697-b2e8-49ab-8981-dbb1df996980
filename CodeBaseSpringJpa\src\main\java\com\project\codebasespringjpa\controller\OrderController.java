package com.project.codebasespringjpa.controller;

import com.project.codebasespringjpa.dto.order.request.OrderRequest;
import com.project.codebasespringjpa.dto.order.request.OrderSearch;
import com.project.codebasespringjpa.dto.order.request.StatusRequest;
import com.project.codebasespringjpa.dto.order.response.OrderResponse;
import com.project.codebasespringjpa.service.interfaces.IOrderService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/orders")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class OrderController {
    IOrderService orderService;

    @GetMapping("find-all")
    public ResponseEntity<Page<OrderResponse>> findAll(@RequestParam(name = "userId", required = false) Long userId,
                                                       @RequestParam(name = "status", required = false) String stauts,
                                                       @RequestParam(name = "keyword", required = false) String keyword,
                                                       @RequestParam(name = "startDate", required = false) LocalDate start,
                                                       @RequestParam(name = "endDate", required = false) LocalDate end,
                                                       @RequestParam(name = "page", defaultValue = "1")Integer page,
                                                       @RequestParam(name = "limit", defaultValue = "5")Integer limit) {

        Pageable pageable = PageRequest.of(page-1, limit);
        OrderSearch orderSearch = OrderSearch.builder()
                .userId(userId)
                .status(stauts)
                .keyword(keyword)
                .startDate(start)
                .endDate(end)
                .build();
        return ResponseEntity.ok(orderService.findAll(orderSearch, pageable));
    }

    @GetMapping("/{id}")
    public ResponseEntity<OrderResponse> findById(@PathVariable Long id) {
        return ResponseEntity.ok(orderService.findById(id));
    }

    @GetMapping("/count/status/{status}")
    public ResponseEntity<Long> countByStatus(@PathVariable String status) {
        return ResponseEntity.ok(orderService.countByStatus(status));
    }

    @GetMapping("/revenue/total")
    public ResponseEntity<Double> getTotalRevenue() {
        return ResponseEntity.ok(orderService.getTotalRevenue());
    }


    @PostMapping("/create")
    public ResponseEntity<OrderResponse> create(@RequestBody OrderRequest request) {
        return new ResponseEntity<>(orderService.create(request), HttpStatus.CREATED);
    }

    @PutMapping("/change-status/{id}")
    public ResponseEntity<OrderResponse> updateStatus(@PathVariable Long id, @RequestBody StatusRequest request) {
        return ResponseEntity.ok(orderService.updateStatus(id, request.getStatus()));
    }

}
