package com.project.codebasespringjpa.controller;

import com.project.codebasespringjpa.dto.wishlist.request.WishlistCreateRequest;
import com.project.codebasespringjpa.dto.wishlist.response.WishlistResponse;
import com.project.codebasespringjpa.service.interfaces.IWishlistService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/wishlists")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class WishlistController {
    IWishlistService wishlistService;

    @GetMapping
    public ResponseEntity<List<WishlistResponse>> findAll() {
        return ResponseEntity.ok(wishlistService.findAll());
    }

    @GetMapping("/{id}")
    public ResponseEntity<WishlistResponse> findById(@PathVariable Long id) {
        return ResponseEntity.ok(wishlistService.findById(id));
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<List<WishlistResponse>> findByUserId(@PathVariable Long userId) {
        return ResponseEntity.ok(wishlistService.findByUserId(userId));
    }

    @PostMapping
    public ResponseEntity<WishlistResponse> create(@RequestBody WishlistCreateRequest request) {
        return new ResponseEntity<>(wishlistService.create(request), HttpStatus.CREATED);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        wishlistService.delete(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/check")
    public ResponseEntity<Boolean> existsByUserIdAndProductId(@RequestParam Long userId, @RequestParam Long productId) {
        return ResponseEntity.ok(wishlistService.existsByUserIdAndProductId(userId, productId));
    }
}
