package com.project.codebasespringjpa.dto.order.response;

import com.project.codebasespringjpa.dto.product.response.ProductResponse;
import com.project.codebasespringjpa.dto.product.response.ProductVariantResponse;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderDetailResponse {
    Long id;
    Long orderId;

    Long idProduct;
    String productName;
    Integer quantity;
    Double price;
    String image;
}
