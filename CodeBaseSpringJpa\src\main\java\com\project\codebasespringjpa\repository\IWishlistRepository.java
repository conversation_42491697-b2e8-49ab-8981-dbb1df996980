package com.project.codebasespringjpa.repository;

import com.project.codebasespringjpa.entity.ProductEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.entity.WishlistEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IWishlistRepository extends JpaRepository<WishlistEntity, Long> {
    List<WishlistEntity> findByUser(UserEntity user);
    Optional<WishlistEntity> findByUserAndProduct(UserEntity user, ProductEntity product);
    boolean existsByUserAndProduct(UserEntity user, ProductEntity product);
    boolean existsByUserIdAndProductId(Long userId, Long productId);
}
