<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sản phẩm - <PERSON> Shop</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.min.css">
    <style>
        .filter-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .price-range-slider {
            margin-top: 10px;
        }
        
        .filter-title {
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .category-filter {
            margin-bottom: 20px;
        }
        
        .sort-select {
            width: 200px;
        }
        
        .pagination-container {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Main Content -->
    <main class="container my-5">
        <h1 class="mb-4">Sản phẩm</h1>
        
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../../index.html">Trang chủ</a></li>
                <li class="breadcrumb-item active" aria-current="page">Sản phẩm</li>
            </ol>
        </nav>
        
        <div class="row">
            <!-- Filters Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="filter-section">
                    <h3 class="filter-title">Bộ lọc sản phẩm</h3>
                    
                    <!-- Category Filter -->
                    <div class="category-filter">
                        <h4 class="h6 mb-3">Danh mục</h4>
                        <div class="form-check mb-2">
                            <input class="form-check-input category-checkbox" type="checkbox" value="bags" id="categoryBags">
                            <label class="form-check-label" for="categoryBags">
                                Túi xách
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input category-checkbox" type="checkbox" value="wallets" id="categoryWallets">
                            <label class="form-check-label" for="categoryWallets">
                                Ví
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input category-checkbox" type="checkbox" value="accessories" id="categoryAccessories">
                            <label class="form-check-label" for="categoryAccessories">
                                Phụ kiện
                            </label>
                        </div>
                    </div>
                    
                    <!-- Price Filter -->
                    <div class="price-filter mb-4">
                        <h4 class="h6 mb-3">Giá</h4>
                        <div class="price-range-slider">
                            <div class="row">
                                <div class="col-6">
                                    <label for="minPrice" class="form-label">Từ</label>
                                    <input type="number" class="form-control" id="minPrice" min="0" step="100000">
                                </div>
                                <div class="col-6">
                                    <label for="maxPrice" class="form-label">Đến</label>
                                    <input type="number" class="form-control" id="maxPrice" min="0" step="100000">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Rating Filter -->
                    <div class="rating-filter mb-4">
                        <h4 class="h6 mb-3">Đánh giá</h4>
                        <div class="form-check mb-2">
                            <input class="form-check-input rating-checkbox" type="checkbox" value="5" id="rating5">
                            <label class="form-check-label" for="rating5">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input rating-checkbox" type="checkbox" value="4" id="rating4">
                            <label class="form-check-label" for="rating4">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="far fa-star text-warning"></i>
                                & Trở lên
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input rating-checkbox" type="checkbox" value="3" id="rating3">
                            <label class="form-check-label" for="rating3">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="far fa-star text-warning"></i>
                                <i class="far fa-star text-warning"></i>
                                & Trở lên
                            </label>
                        </div>
                    </div>
                    
                    <!-- Apply Filters Button -->
                    <button class="btn btn-primary w-100" id="applyFilters">Áp dụng</button>
                </div>
            </div>
            
            <!-- Products -->
            <div class="col-lg-9">
                <!-- Sort and View Options -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="d-flex align-items-center">
                        <label for="sortSelect" class="me-2">Sắp xếp theo:</label>
                        <select class="form-select sort-select" id="sortSelect">
                            <option value="newest">Mới nhất</option>
                            <option value="price-asc">Giá: Thấp đến cao</option>
                            <option value="price-desc">Giá: Cao đến thấp</option>
                            <option value="rating">Đánh giá</option>
                            <option value="popularity">Phổ biến</option>
                        </select>
                    </div>
                    <div class="view-options">
                        <button class="btn btn-outline-secondary me-2" id="gridView">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="btn btn-outline-secondary" id="listView">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Products Container -->
                <div class="row" id="productsContainer">
                    <!-- Products will be loaded here via JavaScript -->
                    <div class="col-12 text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Đang tải sản phẩm...</p>
                    </div>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="Product pagination" class="pagination-container">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be generated via JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.all.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/api.js"></script>
    <script src="../../assets/js/common.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Current page and filters state
            let currentPage = 1;
            let pageSize = 12;
            let viewMode = 'grid'; // 'grid' or 'list'
            let filters = {
                categories: [],
                minPrice: null,
                maxPrice: null,
                ratings: [],
                sort: 'newest'
            };
            
            // Load products on page load
            loadProducts();
            
            // Handle sort change
            document.getElementById('sortSelect').addEventListener('change', function() {
                filters.sort = this.value;
                currentPage = 1;
                loadProducts();
            });
            
            // Handle view mode change
            document.getElementById('gridView').addEventListener('click', function() {
                viewMode = 'grid';
                document.getElementById('productsContainer').classList.remove('list-view');
                loadProducts();
            });
            
            document.getElementById('listView').addEventListener('click', function() {
                viewMode = 'list';
                document.getElementById('productsContainer').classList.add('list-view');
                loadProducts();
            });
            
            // Handle filter application
            document.getElementById('applyFilters').addEventListener('click', function() {
                // Get selected categories
                filters.categories = [];
                document.querySelectorAll('.category-checkbox:checked').forEach(checkbox => {
                    filters.categories.push(checkbox.value);
                });
                
                // Get price range
                filters.minPrice = document.getElementById('minPrice').value || null;
                filters.maxPrice = document.getElementById('maxPrice').value || null;
                
                // Get ratings
                filters.ratings = [];
                document.querySelectorAll('.rating-checkbox:checked').forEach(checkbox => {
                    filters.ratings.push(parseInt(checkbox.value));
                });
                
                // Reset to first page and load products
                currentPage = 1;
                loadProducts();
            });
            
            // Load products with current filters and pagination
            function loadProducts() {
                const productsContainer = document.getElementById('productsContainer');
                productsContainer.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Đang tải sản phẩm...</p>
                    </div>
                `;
                apiService.getProducts({ page: currentPage, limit: pageSize }).then(response => {
                    const products = response.items || response.data || [];
                    let html = '';
                    products.forEach(product => {
                        html += `<div class="col-md-4 mb-4"><div class="card h-100"><img src="${product.image}" class="card-img-top" alt="${product.name}"><div class="card-body"><h5 class="card-title">${product.name}</h5><p class="card-text">${product.price.toLocaleString()} đ</p><button class="btn btn-primary btn-add-to-cart" data-id="${product.id}">Thêm vào giỏ hàng</button></div></div></div>`;
                    });
                    productsContainer.innerHTML = html;
                    // Gán sự kiện cho nút Thêm vào giỏ hàng
                    document.querySelectorAll('.btn-add-to-cart').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const productId = this.getAttribute('data-id');
                            addToCart(productId, 1);
                        });
                    });
                }).catch(() => {
                    productsContainer.innerHTML = '<div class="col-12 text-center py-5 text-danger">Không thể tải sản phẩm.</div>';
                });
            }
            
            // Create product card HTML
            function createProductCard(product, viewMode) {
                const discountPercent = product.salePrice ? Math.round((1 - product.salePrice / product.price) * 100) : 0;
                
                if (viewMode === 'grid') {
                    return `
                        <div class="product-card">
                            <div class="product-img-container">
                                <img src="${product.imageUrl}" alt="${product.name}" class="product-img">
                                ${discountPercent > 0 ? `<span class="badge bg-danger position-absolute top-0 start-0 m-2">-${discountPercent}%</span>` : ''}
                            </div>
                            <div class="product-info">
                                <h5 class="product-title">
                                    <a href="detail.html?id=${product.id}">${product.name}</a>
                                </h5>
                                <div class="product-rating">
                                    ${createRatingStars(product.rating)}
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        ${product.salePrice ? 
                                            `<span class="product-price">${formatCurrency(product.salePrice)}</span>
                                            <small class="text-muted text-decoration-line-through">${formatCurrency(product.price)}</small>` : 
                                            `<span class="product-price">${formatCurrency(product.price)}</span>`
                                        }
                                    </div>
                                    <button class="btn btn-sm btn-primary" onclick="addToCart(${product.id})">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    return `
                        <div class="card">
                            <div class="row g-0">
                                <div class="col-md-3">
                                    <img src="${product.imageUrl}" alt="${product.name}" class="img-fluid rounded-start h-100 object-fit-cover">
                                    ${discountPercent > 0 ? `<span class="badge bg-danger position-absolute top-0 start-0 m-2">-${discountPercent}%</span>` : ''}
                                </div>
                                <div class="col-md-9">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <a href="detail.html?id=${product.id}">${product.name}</a>
                                        </h5>
                                        <div class="product-rating mb-2">
                                            ${createRatingStars(product.rating)}
                                        </div>
                                        <div class="mb-3">
                                            ${product.salePrice ? 
                                                `<span class="product-price">${formatCurrency(product.salePrice)}</span>
                                                <small class="text-muted text-decoration-line-through">${formatCurrency(product.price)}</small>` : 
                                                `<span class="product-price">${formatCurrency(product.price)}</span>`
                                            }
                                        </div>
                                        <p class="card-text">Sản phẩm thời trang cao cấp, thiết kế hiện đại, chất liệu bền đẹp.</p>
                                        <button class="btn btn-primary" onclick="addToCart(${product.id})">
                                            <i class="fas fa-shopping-cart me-2"></i> Thêm vào giỏ hàng
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }
            
            // Create rating stars HTML
            function createRatingStars(rating) {
                let starsHtml = '';
                for (let i = 1; i <= 5; i++) {
                    if (i <= rating) {
                        starsHtml += '<i class="fas fa-star"></i>';
                    } else if (i - 0.5 <= rating) {
                        starsHtml += '<i class="fas fa-star-half-alt"></i>';
                    } else {
                        starsHtml += '<i class="far fa-star"></i>';
                    }
                }
                return starsHtml;
            }
            
            // Generate pagination
            function generatePagination(totalPages) {
                const pagination = document.getElementById('pagination');
                pagination.innerHTML = '';
                
                // Previous button
                const prevLi = document.createElement('li');
                prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
                prevLi.innerHTML = `
                    <a class="page-link" href="#" aria-label="Previous" ${currentPage > 1 ? 'onclick="changePage(' + (currentPage - 1) + '); return false;"' : ''}>
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                `;
                pagination.appendChild(prevLi);
                
                // Page numbers
                for (let i = 1; i <= totalPages; i++) {
                    const pageLi = document.createElement('li');
                    pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
                    pageLi.innerHTML = `
                        <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
                    `;
                    pagination.appendChild(pageLi);
                }
                
                // Next button
                const nextLi = document.createElement('li');
                nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
                nextLi.innerHTML = `
                    <a class="page-link" href="#" aria-label="Next" ${currentPage < totalPages ? 'onclick="changePage(' + (currentPage + 1) + '); return false;"' : ''}>
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                `;
                pagination.appendChild(nextLi);
            }
            
            // Change page
            window.changePage = function(page) {
                currentPage = page;
                loadProducts();
                window.scrollTo(0, 0);
            };
        });
    </script>
</body>
</html>
