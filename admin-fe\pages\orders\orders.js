document.addEventListener('DOMContentLoaded', function() {
  // Kiểm tra đăng nhập
  if (!checkAuth()) {
    return;
  }
  
  // Load layout components
  loadLayoutComponents();
  
  // Khởi tạo biến toàn cục
  let currentPage = 0;
  let pageSize = 10;
  let totalPages = 0;
  
  // Load dữ liệu ban đầu
  loadOrders(currentPage, pageSize);
  
  // Xử lý sự kiện
  setupEventListeners();
  
  // Hàm load layout components
  function loadLayoutComponents() {
    // Load header
    fetch('/layout/header/header.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('header-container').innerHTML = html;
        
        // Load header script after HTML is inserted
        const headerScript = document.createElement('script');
        headerScript.src = '/layout/header/header.js';
        document.body.appendChild(headerScript);
      });
    
    // Load sidebar
    fetch('/layout/sidebar/sidebar.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('sidebar-container').innerHTML = html;
        
        // Load sidebar script after HTML is inserted
        const sidebarScript = document.createElement('script');
        sidebarScript.src = '/layout/sidebar/sidebar.js';
        document.body.appendChild(sidebarScript);
      });
    
    // Load footer
    fetch('/layout/footer/footer.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('footer-container').innerHTML = html;
      });
  }
  
  // Hàm load đơn hàng
  function loadOrders(page, size) {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    // Hiển thị loading
    document.getElementById('orderTable').innerHTML = `
      <tr>
        <td colspan="7" class="text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Đang tải...</span>
          </div>
        </td>
      </tr>
    `;
    
    // Lấy các tham số lọc
    const status = document.getElementById('filterStatus').value;
    const keyword = document.getElementById('searchOrder').value;
    
    // Xây dựng URL với các tham số lọc và phân trang
    let url = `http://localhost:8080/api/orders/find-all?page=${page+1}&limit=${size}`;
    
    if (status) url += `&status=${status}`;
    if (keyword) url += `&keyword=${encodeURIComponent(keyword)}`;
    
    axios.get(url)
      .then(response => {
        let orders = response.data.content || [];
        let totalElements = response.data.totalElements || orders.length;
        totalPages = response.data.totalPages || 1;
        
        // Hiển thị tổng số đơn hàng
        document.getElementById('totalOrders').textContent = totalElements;
        
        // Hiển thị danh sách đơn hàng
        displayOrders(orders);
        
        // Hiển thị phân trang
        displayPagination(page, totalPages);
      })
      .catch(error => {
        handleApiError(error);
        
        // Hiển thị thông báo lỗi trong bảng
        document.getElementById('orderTable').innerHTML = `
          <tr>
            <td colspan="7" class="text-center text-danger">
              Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại sau.
            </td>
          </tr>
        `;
      });
  }
  
  // Hàm hiển thị danh sách đơn hàng
  function displayOrders(orders) {
    const tableBody = document.getElementById('orderTable');
    tableBody.innerHTML = '';
    
    if (!orders || orders.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="7" class="text-center">
            Không có đơn hàng nào
          </td>
        </tr>
      `;
      return;
    }
    
    orders.forEach(order => {
      const row = document.createElement('tr');
      
      // Tạo badge cho trạng thái
      let statusBadge = '';
      let statusText = '';
      
      switch (order.status) {
        case 'PENDING':
          statusBadge = 'badge-pending';
          statusText = 'Chờ xử lý';
          break;
        case 'PROCESSING':
          statusBadge = 'badge-processing';
          statusText = 'Đang xử lý';
          break;
        case 'SHIPPED':
          statusBadge = 'badge-shipped';
          statusText = 'Đang giao';
          break;
        case 'DELIVERED':
          statusBadge = 'badge-delivered';
          statusText = 'Đã giao';
          break;
        case 'CANCELLED':
          statusBadge = 'badge-cancelled';
          statusText = 'Đã hủy';
          break;
        default:
          statusBadge = 'bg-secondary';
          statusText = order.status || 'Không xác định';
      }
      
      // Xác định nút hành động
      let actionButton = '';
      if (order.status === 'PENDING') {
          actionButton = `<button class="btn btn-sm btn-success btn-approve" data-id="${order.id}">Duyệt đơn</button>`;
      }
      
      row.innerHTML = `
        <td>#${order.id}</td>
        <td>${order.username || 'N/A'}</td>
        <td>${order.phone || 'N/A'}</td>
        <td>${order.orderDate ? formatDate(order.orderDate) : ''}</td>
        <td>${formatCurrency(order.totalAmount)}</td>
        <td><span class="badge ${statusBadge}">${statusText}</span></td>
        <td>
            ${actionButton}
        </td>
      `;
      
      tableBody.appendChild(row);
    });
    
    // Thêm sự kiện cho nút duyệt đơn (chỉ nút duyệt)
    document.querySelectorAll('.btn-approve').forEach(button => {
      button.addEventListener('click', function() {
        const orderId = this.getAttribute('data-id');
        changeOrderStatus(orderId, 'PENDING', 'PROCESSING');
      });
    });
  }
  
  // Hàm hiển thị phân trang
  function displayPagination(currentPage, totalPages) {
    const paginationContainer = document.getElementById('pagination');
    paginationContainer.innerHTML = '';
    
    if (totalPages <= 1) {
      return;
    }
    
    const pagination = createPagination(currentPage, totalPages, (page) => {
      currentPage = page;
      const status = document.getElementById('filterStatus').value;
      const keyword = document.getElementById('searchOrder').value;
      loadOrders(page, pageSize, status, keyword);
    });
    
    paginationContainer.appendChild(pagination);
  }
  
  // Hàm đổi trạng thái đơn hàng
  function changeOrderStatus(orderId, fromStatus, toStatus) {
      // Chỉ cho phép đổi từ fromStatus sang toStatus
      if (fromStatus !== 'PENDING' || toStatus !== 'PROCESSING') {
          console.error('Chỉ được đổi trạng thái từ PENDING sang PROCESSING');
          return;
      }

    Swal.fire({
      title: 'Xác nhận duyệt đơn hàng',
      text: 'Bạn có chắc chắn muốn duyệt đơn hàng này không?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Duyệt',
      cancelButtonText: 'Hủy'
    }).then((result) => {
      if (result.isConfirmed) {
        const token = localStorage.getItem('token');
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        axios.put(`http://localhost:8080/api/orders/change-status/${orderId}`, { status: toStatus })
          .then(response => {
            Swal.fire({ icon: 'success', title: 'Thành công', text: 'Đơn hàng đã được duyệt.' });
            loadOrders(currentPage, pageSize, document.getElementById('filterStatus').value, document.getElementById('searchOrder').value);
          })
          .catch(error => {
            handleApiError(error);
          });
      }
    });
  }
  
  // Hàm thiết lập các sự kiện
  function setupEventListeners() {
    // Sự kiện nút tìm kiếm
    const btnSearch = document.getElementById('btnSearch');
    if(btnSearch) {
        btnSearch.addEventListener('click', function() {
            currentPage = 0;
            const status = document.getElementById('filterStatus').value;
            const keyword = document.getElementById('searchOrder').value;
            loadOrders(currentPage, pageSize, status, keyword);
        });
    }
    
    // Sự kiện thay đổi bộ lọc trạng thái
    const filterStatus = document.getElementById('filterStatus');
    if(filterStatus) {
        filterStatus.addEventListener('change', function() {
            currentPage = 0;
            const status = filterStatus.value;
            const keyword = document.getElementById('searchOrder').value;
            loadOrders(currentPage, pageSize, status, keyword);
        });
    }
  }
  
  // Hàm format ngày
  function formatDate(dateString) {
    if (!dateString) return '';
    const d = new Date(dateString);
    return d.toLocaleString('vi-VN');
  }
  
  // Hàm format tiền tệ
  function formatCurrency(amount) {
    if (amount === null || amount === undefined) return '';
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
  }
});
