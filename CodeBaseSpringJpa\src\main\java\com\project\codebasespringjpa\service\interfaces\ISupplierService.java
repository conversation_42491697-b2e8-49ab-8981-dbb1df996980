package com.project.codebasespringjpa.service.interfaces;

import com.project.codebasespringjpa.dto.supplier.request.SupplierCreateRequest;
import com.project.codebasespringjpa.dto.supplier.request.SupplierUpdateRequest;
import com.project.codebasespringjpa.dto.supplier.response.SupplierResponse;

import java.util.List;

public interface ISupplierService {
    List<SupplierResponse> findAll(String keyword);
    SupplierResponse findById(Long id);
    SupplierResponse findByName(String name);
    SupplierResponse findByEmail(String email);
    SupplierResponse create(SupplierCreateRequest request);
    SupplierResponse update(Long id, SupplierUpdateRequest request);
    void delete(Long id);
    boolean existsByName(String name);
    boolean existsByEmail(String email);
}
