package com.project.codebasespringjpa.service.interfaces;

import com.project.codebasespringjpa.dto.cart.request.CartRequest;
import com.project.codebasespringjpa.dto.cart.response.CartResponse;
import com.project.codebasespringjpa.entity.CartEntity;

import java.util.List;

public interface ICartService {
    List<CartResponse> findByUserId(Long userId);
    CartResponse create(CartRequest request);
    void updateItem(Long cartId, Integer quantity);
    void delete(Long cartId);
    void deleteAll(Long userId);
}
