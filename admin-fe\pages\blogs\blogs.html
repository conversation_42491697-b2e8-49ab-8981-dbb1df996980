<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON><PERSON> l<PERSON> b<PERSON><PERSON> viết - Tina Shop Admin</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.3/dist/sweetalert2.min.css">
  <!-- Common CSS -->
  <link rel="stylesheet" href="../../assets/css/common.css">
  <!-- Layout CSS -->
  <link rel="stylesheet" href="../../layout/header/header.css">
  <link rel="stylesheet" href="../../layout/sidebar/sidebar.css">
  <link rel="stylesheet" href="../../layout/footer/footer.css">
  <!-- Page CSS -->
  <link rel="stylesheet" href="blogs.css">
</head>
<body>
  <!-- Header -->
  <div id="header-container"></div>
  
  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <div id="sidebar-container"></div>
      
      <!-- Main content -->
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
          <h1 class="h2">Quản lý bài viết</h1>
          <div class="btn-toolbar mb-2 mb-md-0">
            <button type="button" class="btn btn-sm btn-primary me-2" id="btnAddBlog">
              <i class="bi bi-plus-circle"></i> Thêm bài viết
            </button>
            <div class="btn-group me-2">
              <button type="button" class="btn btn-sm btn-outline-secondary">Xuất Excel</button>
              <button type="button" class="btn btn-sm btn-outline-secondary">In</button>
            </div>
          </div>
        </div>
        
        <!-- Bộ lọc -->
        <div class="row mb-3">
          <div class="col-md-12">
            <div class="card">
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-md-12">
                    <label for="searchBlog" class="form-label">Tìm kiếm</label>
                    <div class="input-group">
                      <input type="text" class="form-control" id="searchBlog" placeholder="Tiêu đề, nội dung...">
                      <button class="btn btn-outline-secondary" type="button" id="btnSearch">
                        <i class="bi bi-search"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Danh sách bài viết -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped table-hover">
                    <thead>
                      <tr>
                        <th width="50">#</th>
                        <th width="80">Ảnh</th>
                        <th>Tiêu đề</th>
                        <th>Ngày tạo</th>
                        <th width="150">Hành động</th>
                      </tr>
                    </thead>
                    <tbody id="blogTable">
                      <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                      <tr>
                        <td colspan="5" class="text-center">Đang tải dữ liệu...</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                
                <!-- Phân trang -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                  <div>
                    <span id="totalBlogs">0</span> bài viết
                  </div>
                  <div id="pagination" class="pagination-container">
                    <!-- Phân trang sẽ được thêm bằng JavaScript -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
  
  <!-- Footer -->
  <div id="footer-container"></div>
  
  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <!-- Axios -->
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.3/dist/sweetalert2.all.min.js"></script>
  <!-- Common JS -->
  <script src="../../assets/js/common.js"></script>
  <!-- Layout JS -->
  <script src="../../layout/header/header.js"></script>
  <script src="../../layout/sidebar/sidebar.js"></script>
  <!-- Page JS -->
  <script src="blogs.js"></script>
  <!-- CKEditor 5 Classic build -->
  <script src="https://cdn.ckeditor.com/ckeditor5/36.0.1/classic/ckeditor.js"></script>
  
  <!-- Modal thêm/sửa bài viết -->
  <div class="modal fade" id="blogModal" tabindex="-1" aria-labelledby="blogModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="blogModalLabel">Thêm bài viết</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="blogForm">
            <input type="hidden" id="blogId">
            <div class="mb-3">
              <label for="blogTitle" class="form-label">Tiêu đề</label>
              <input type="text" class="form-control" id="blogTitle" required>
            </div>
            <div class="mb-3">
              <label for="blogContent" class="form-label">Nội dung</label>
              <textarea class="form-control" id="blogContent" rows="5" required></textarea>
            </div>
            <div class="mb-3">
              <label for="blogImage" class="form-label">Ảnh đại diện</label>
              <input type="file" class="form-control" id="blogImage" accept="image/*">
              <div id="blogImagePreview" class="mt-2"></div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="button" class="btn btn-primary" id="btnSaveBlog">Lưu</button>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
