<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Shopping Flow - Tina Shop</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-5">Test Shopping Flow</h1>
        
        <!-- Auth Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Trạng thái đăng nhập</h5>
                    </div>
                    <div class="card-body">
                        <div id="auth-status">
                            <p>Đang kiểm tra...</p>
                        </div>
                        <div id="auth-actions">
                            <button class="btn btn-primary me-2" onclick="testLogin()">Test Login</button>
                            <button class="btn btn-secondary me-2" onclick="testRegister()">Test Register</button>
                            <button class="btn btn-danger" onclick="logout()">Logout</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Sản phẩm test</h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="products-container">
                            <!-- Products will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cart -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Giỏ hàng <span id="cart-count" class="badge bg-primary">0</span></h5>
                    </div>
                    <div class="card-body">
                        <div id="cart-items">
                            <!-- Cart items will be loaded here -->
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-success" onclick="testCheckout()">Test Checkout</button>
                            <button class="btn btn-warning" onclick="loadCart()">Refresh Cart</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-info me-2" onclick="loadProducts()">Load Products</button>
                        <button class="btn btn-info me-2" onclick="loadCart()">Load Cart</button>
                        <button class="btn btn-warning me-2" onclick="clearCart()">Clear Cart</button>
                        <button class="btn btn-secondary" onclick="checkAuthStatus()">Check Auth</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.all.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/services/api-config.js"></script>
    <script src="assets/js/services/auth-service.js"></script>
    <script src="assets/js/services/cart-service.js"></script>
    <script src="assets/js/services/order-service.js"></script>

    <script>
        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(amount);
        }

        // Check auth status
        function checkAuthStatus() {
            const token = localStorage.getItem('token');
            const userId = localStorage.getItem('userId');
            const username = localStorage.getItem('username');
            const fullName = localStorage.getItem('fullName');
            
            const authStatusDiv = document.getElementById('auth-status');
            
            if (token && userId) {
                authStatusDiv.innerHTML = `
                    <div class="alert alert-success">
                        <strong>Đã đăng nhập</strong><br>
                        User ID: ${userId}<br>
                        Username: ${username}<br>
                        Full Name: ${fullName}<br>
                        Token: ${token.substring(0, 20)}...
                    </div>
                `;
            } else {
                authStatusDiv.innerHTML = `
                    <div class="alert alert-warning">
                        <strong>Chưa đăng nhập</strong>
                    </div>
                `;
            }
        }

        // Test login
        async function testLogin() {
            try {
                const response = await AuthService.login('<EMAIL>', 'password123');
                Swal.fire('Success', 'Login successful!', 'success');
                checkAuthStatus();
            } catch (error) {
                Swal.fire('Error', error.message, 'error');
            }
        }

        // Test register
        async function testRegister() {
            try {
                const userData = {
                    fullname: 'Test User',
                    email: '<EMAIL>',
                    phone: '0123456789',
                    password: 'password123',
                    username: '<EMAIL>',
                    address: 'Test Address'
                };
                
                const response = await AuthService.register(userData);
                Swal.fire('Success', 'Register successful!', 'success');
            } catch (error) {
                Swal.fire('Error', error.message, 'error');
            }
        }

        // Logout
        function logout() {
            AuthService.logout();
            checkAuthStatus();
        }

        // Load products
        async function loadProducts() {
            try {
                const products = await apiRequest('products/find-all?limit=4');
                const productList = products.content || products;
                
                const container = document.getElementById('products-container');
                container.innerHTML = '';
                
                productList.forEach(product => {
                    const productCard = `
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <img src="${product.image || 'https://via.placeholder.com/200'}" class="card-img-top" style="height: 200px; object-fit: cover;">
                                <div class="card-body">
                                    <h6 class="card-title">${product.name}</h6>
                                    <p class="card-text">${formatCurrency(product.price)}</p>
                                    <button class="btn btn-primary btn-sm" onclick="addToCart(${product.id})">
                                        <i class="fas fa-cart-plus"></i> Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                    container.innerHTML += productCard;
                });
            } catch (error) {
                Swal.fire('Error', 'Failed to load products: ' + error.message, 'error');
            }
        }

        // Add to cart
        async function addToCart(productId) {
            try {
                const userId = localStorage.getItem('userId');
                if (!userId) {
                    Swal.fire('Error', 'Please login first!', 'error');
                    return;
                }

                // Get product details
                const product = await apiRequest(`products/${productId}`);
                
                const cartData = {
                    productId: productId,
                    quantity: 1,
                    price: product.salePrice || product.price,
                    userId: parseInt(userId)
                };

                await CartService.addItem(cartData);
                Swal.fire('Success', 'Added to cart!', 'success');
                loadCart();
            } catch (error) {
                Swal.fire('Error', 'Failed to add to cart: ' + error.message, 'error');
            }
        }

        // Load cart
        async function loadCart() {
            try {
                const userId = localStorage.getItem('userId');
                if (!userId) {
                    document.getElementById('cart-items').innerHTML = '<p>Please login to view cart</p>';
                    document.getElementById('cart-count').textContent = '0';
                    return;
                }

                const cartItems = await CartService.getByUserId(userId);
                const container = document.getElementById('cart-items');
                
                if (!cartItems || cartItems.length === 0) {
                    container.innerHTML = '<p>Cart is empty</p>';
                    document.getElementById('cart-count').textContent = '0';
                    return;
                }

                let total = 0;
                let html = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>Product</th><th>Qty</th><th>Price</th><th>Total</th><th>Action</th></tr></thead><tbody>';
                
                cartItems.forEach(item => {
                    const itemTotal = item.price * item.quantity;
                    total += itemTotal;
                    html += `
                        <tr>
                            <td>${item.productName}</td>
                            <td>${item.quantity}</td>
                            <td>${formatCurrency(item.price)}</td>
                            <td>${formatCurrency(itemTotal)}</td>
                            <td>
                                <button class="btn btn-danger btn-sm" onclick="removeFromCart(${item.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                });
                
                html += `</tbody><tfoot><tr><th colspan="3">Total:</th><th>${formatCurrency(total)}</th><th></th></tr></tfoot></table></div>`;
                container.innerHTML = html;
                
                const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
                document.getElementById('cart-count').textContent = totalItems;
            } catch (error) {
                Swal.fire('Error', 'Failed to load cart: ' + error.message, 'error');
            }
        }

        // Remove from cart
        async function removeFromCart(itemId) {
            try {
                await CartService.removeItem(itemId);
                Swal.fire('Success', 'Removed from cart!', 'success');
                loadCart();
            } catch (error) {
                Swal.fire('Error', 'Failed to remove from cart: ' + error.message, 'error');
            }
        }

        // Clear cart
        async function clearCart() {
            try {
                const userId = localStorage.getItem('userId');
                if (!userId) {
                    Swal.fire('Error', 'Please login first!', 'error');
                    return;
                }

                await CartService.clear(userId);
                Swal.fire('Success', 'Cart cleared!', 'success');
                loadCart();
            } catch (error) {
                Swal.fire('Error', 'Failed to clear cart: ' + error.message, 'error');
            }
        }

        // Test checkout
        async function testCheckout() {
            try {
                const userId = localStorage.getItem('userId');
                if (!userId) {
                    Swal.fire('Error', 'Please login first!', 'error');
                    return;
                }

                const cartItems = await CartService.getByUserId(userId);
                if (!cartItems || cartItems.length === 0) {
                    Swal.fire('Error', 'Cart is empty!', 'error');
                    return;
                }

                const total = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                
                const orderData = {
                    userId: parseInt(userId),
                    username: localStorage.getItem('username'),
                    phone: '0123456789',
                    totalAmount: total,
                    status: 'PENDING',
                    shippingAddress: 'Test Address',
                    paymentMethod: 'COD',
                    paymentStatus: 'PENDING',
                    note: 'Test order',
                    orders: cartItems.map(item => ({
                        productId: item.productId,
                        quantity: item.quantity,
                        price: item.price
                    }))
                };

                const order = await OrderService.create(orderData);
                await CartService.clear(userId);
                
                Swal.fire('Success', `Order created successfully! Order ID: ${order.id}`, 'success');
                loadCart();
            } catch (error) {
                Swal.fire('Error', 'Failed to create order: ' + error.message, 'error');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
            loadProducts();
            loadCart();
        });
    </script>
</body>
</html>
