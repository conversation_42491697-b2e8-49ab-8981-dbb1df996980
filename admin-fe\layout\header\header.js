document.addEventListener('DOMContentLoaded', function() {
  // Hiển thị tên người dùng
  displayUserInfo();

  // <PERSON><PERSON> lý sự kiện đăng xuất
  const btnLogout = document.getElementById('btnLogout');
  if (btnLogout) {
    btnLogout.addEventListener('click', function(e) {
      e.preventDefault();

      // Hiển thị xác nhận đăng xuất
      Swal.fire({
        title: 'Đăng xuất',
        text: 'Bạn có chắc chắn muốn đăng xuất?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Đồng ý',
        cancelButtonText: 'Hủy'
      }).then((result) => {
        if (result.isConfirmed) {
          // Xóa token từ localStorage
          localStorage.removeItem('token');
          localStorage.removeItem('user');

          // Chuyển hướng về trang đăng nhập
          window.location.href = '../../pages/login/login.html';
        }
      });
    });
  }

  // Xử lý sự kiện tìm kiếm
  const searchInput = document.querySelector('.form-control-dark');
  if (searchInput) {
    searchInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        const keyword = this.value.trim();
        if (keyword) {
          // Thực hiện tìm kiếm (có thể chuyển hướng đến trang kết quả tìm kiếm)
          alert('Đang tìm kiếm: ' + keyword);
        }
      }
    });
  }
});

// Hiển thị thông tin người dùng
function displayUserInfo() {
  const userElement = document.getElementById('currentUserName');
  if (userElement) {
    const userJson = localStorage.getItem('user');
    if (userJson) {
      try {
        const user = JSON.parse(userJson);
        userElement.textContent = user.fullName || 'Admin';
      } catch (error) {
        console.error('Lỗi khi phân tích dữ liệu người dùng:', error);
        userElement.textContent = 'Admin';
      }
    }
  }
}
