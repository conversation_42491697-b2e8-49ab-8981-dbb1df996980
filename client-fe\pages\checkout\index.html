<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thanh toán - Tina Shop</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.min.css">
    <style>
        .checkout-section {
            margin-bottom: 30px;
        }
        
        .checkout-section-title {
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .order-summary {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .order-summary-title {
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .order-summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .order-summary-total {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .order-summary-total-amount {
            color: var(--primary-color);
        }
        
        .payment-method {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-method:hover {
            border-color: var(--primary-color);
        }
        
        .payment-method.active {
            border-color: var(--primary-color);
            background-color: rgba(255, 107, 107, 0.05);
        }
        
        .payment-method-header {
            display: flex;
            align-items: center;
        }
        
        .payment-method-radio {
            margin-right: 10px;
        }
        
        .payment-method-title {
            font-weight: 600;
            margin-bottom: 0;
        }
        
        .payment-method-description {
            margin-top: 10px;
            margin-left: 25px;
            color: #6c757d;
        }
        
        .payment-method-icon {
            margin-left: auto;
            font-size: 1.5rem;
        }
        
        .order-items {
            margin-top: 20px;
        }
        
        .order-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .order-item-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 5px;
            margin-right: 15px;
        }
        
        .order-item-details {
            flex-grow: 1;
        }
        
        .order-item-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .order-item-price {
            color: var(--primary-color);
        }
        
        .order-item-quantity {
            margin-left: auto;
            background-color: #f8f9fa;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Main Content -->
    <main class="container my-5">
        <h1 class="mb-4">Thanh toán</h1>
        
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../../index.html">Trang chủ</a></li>
                <li class="breadcrumb-item"><a href="../cart/index.html">Giỏ hàng</a></li>
                <li class="breadcrumb-item active" aria-current="page">Thanh toán</li>
            </ol>
        </nav>
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Checkout Form -->
                <form id="checkoutForm">
                    <!-- Billing Information -->
                    <div class="checkout-section">
                        <h3 class="checkout-section-title">Thông tin thanh toán</h3>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">Họ</label>
                                <input type="text" class="form-control" id="firstName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">Tên</label>
                                <input type="text" class="form-control" id="lastName" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">Số điện thoại</label>
                            <input type="tel" class="form-control" id="phone" required>
                        </div>
                    </div>
                    
                    <!-- Shipping Information -->
                    <div class="checkout-section">
                        <h3 class="checkout-section-title">Thông tin giao hàng</h3>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="sameAsBilling" checked>
                            <label class="form-check-label" for="sameAsBilling">Địa chỉ giao hàng giống với địa chỉ thanh toán</label>
                        </div>
                        <div id="shippingAddressContainer" style="display: none;">
                            <!-- Shipping address fields will be shown here when checkbox is unchecked -->
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Địa chỉ</label>
                            <input type="text" class="form-control" id="address" required>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="province" class="form-label">Tỉnh/Thành phố</label>
                                <select class="form-select" id="province" required>
                                    <option value="">Chọn tỉnh/thành phố</option>
                                    <!-- Provinces will be loaded here -->
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="district" class="form-label">Quận/Huyện</label>
                                <select class="form-select" id="district" required disabled>
                                    <option value="">Chọn quận/huyện</option>
                                    <!-- Districts will be loaded here -->
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="ward" class="form-label">Phường/Xã</label>
                                <select class="form-select" id="ward" required disabled>
                                    <option value="">Chọn phường/xã</option>
                                    <!-- Wards will be loaded here -->
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="note" class="form-label">Ghi chú đơn hàng (tùy chọn)</label>
                            <textarea class="form-control" id="note" rows="3"></textarea>
                        </div>
                    </div>
                    
                    <!-- Payment Methods -->
                    <div class="checkout-section">
                        <h3 class="checkout-section-title">Phương thức thanh toán</h3>
                        <div class="payment-method active" onclick="selectPaymentMethod(this, 'cod')">
                            <div class="payment-method-header">
                                <input type="radio" name="paymentMethod" value="cod" class="payment-method-radio" checked>
                                <h5 class="payment-method-title">Thanh toán khi nhận hàng (COD)</h5>
                                <div class="payment-method-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                            </div>
                            <div class="payment-method-description">
                                Thanh toán bằng tiền mặt khi nhận hàng
                            </div>
                        </div>
                        <div class="payment-method" onclick="selectPaymentMethod(this, 'bank')">
                            <div class="payment-method-header">
                                <input type="radio" name="paymentMethod" value="bank" class="payment-method-radio">
                                <h5 class="payment-method-title">Chuyển khoản ngân hàng</h5>
                                <div class="payment-method-icon">
                                    <i class="fas fa-university"></i>
                                </div>
                            </div>
                            <div class="payment-method-description">
                                Thực hiện thanh toán vào tài khoản ngân hàng của chúng tôi. Vui lòng sử dụng Mã đơn hàng của bạn trong phần Nội dung thanh toán. Đơn hàng sẽ được giao sau khi tiền đã được chuyển.
                            </div>
                        </div>
                        <div class="payment-method" onclick="selectPaymentMethod(this, 'momo')">
                            <div class="payment-method-header">
                                <input type="radio" name="paymentMethod" value="momo" class="payment-method-radio">
                                <h5 class="payment-method-title">Thanh toán qua Ví MoMo</h5>
                                <div class="payment-method-icon">
                                    <i class="fas fa-wallet"></i>
                                </div>
                            </div>
                            <div class="payment-method-description">
                                Thanh toán qua ví điện tử MoMo
                            </div>
                        </div>
                        <div class="payment-method" onclick="selectPaymentMethod(this, 'vnpay')">
                            <div class="payment-method-header">
                                <input type="radio" name="paymentMethod" value="vnpay" class="payment-method-radio">
                                <h5 class="payment-method-title">Thanh toán qua VNPAY</h5>
                                <div class="payment-method-icon">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                            </div>
                            <div class="payment-method-description">
                                Thanh toán trực tuyến qua VNPAY
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="../cart/index.html" class="btn btn-outline-primary me-md-2">
                            <i class="fas fa-arrow-left me-2"></i> Quay lại giỏ hàng
                        </a>
                        <button type="submit" class="btn btn-primary">
                            Đặt hàng <i class="fas fa-arrow-right ms-2"></i>
                        </button>
                    </div>
                </form>
            </div>
            
            <div class="col-lg-4 mt-4 mt-lg-0">
                <!-- Order Summary -->
                <div class="order-summary">
                    <h4 class="order-summary-title">Đơn hàng của bạn</h4>
                    
                    <div id="order-items-container">
                        <!-- Order items will be loaded here -->
                    </div>
                    
                    <div class="order-summary-item">
                        <span>Tạm tính</span>
                        <span id="subtotal">0₫</span>
                    </div>
                    <div class="order-summary-item">
                        <span>Phí vận chuyển</span>
                        <span id="shipping">0₫</span>
                    </div>
                    <div class="order-summary-item" id="discount-container" style="display: none;">
                        <span>Giảm giá</span>
                        <span id="discount">0₫</span>
                    </div>
                    <div class="order-summary-total">
                        <span>Tổng cộng</span>
                        <span class="order-summary-total-amount" id="total">0₫</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.all.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/services/api-config.js"></script>
    <script src="../../assets/js/services/cart-service.js"></script>
    <script src="../../assets/js/services/order-service.js"></script>
    <script src="../../assets/js/common.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            const userId = localStorage.getItem('userId');
            if (!userId) {
                window.location.href = '../login/index.html?redirect=' + encodeURIComponent(window.location.pathname);
                return;
            }

            // Load cart items
            loadCartItems();
            
            // Load provinces for shipping address
            loadProvinces();
        });

        // Load cart items
        async function loadCartItems() {
            try {
                const userId = localStorage.getItem('userId');
                if (!userId) {
                    throw new Error('User not logged in');
                }

                const cartItems = await CartService.getByUserId(userId);
                if (!cartItems || cartItems.length === 0) {
                    window.location.href = '../cart/index.html';
                    return;
                }

                // Calculate totals
                let subtotal = 0;
                cartItems.forEach(item => {
                    subtotal += item.price * item.quantity;
                });
                const shipping = 30000; // Fixed shipping cost
                const total = subtotal + shipping;

                // Update order summary
                document.getElementById('subtotal').textContent = formatCurrency(subtotal);
                document.getElementById('shipping').textContent = formatCurrency(shipping);
                document.getElementById('total').textContent = formatCurrency(total);

                // Render cart items
                const orderItemsContainer = document.getElementById('orderItemsContainer');
                orderItemsContainer.innerHTML = cartItems.map(item => `
                    <div class="order-item">
                        <img src="${item.image}" alt="${item.productName}" class="order-item-image">
                        <div class="order-item-details">
                            <h6 class="order-item-title">${item.productName}</h6>
                            <div class="order-item-price">${formatCurrency(item.price)}</div>
                        </div>
                        <div class="order-item-quantity">x${item.quantity}</div>
                    </div>
                `).join('');

                // Store cart items for order creation
                window.cartItems = cartItems;
            } catch (error) {
                console.error('Error loading cart items:', error);
                showToast('Không thể tải giỏ hàng. Vui lòng thử lại sau.', 'error');
            }
        }

        // Submit order
        async function submitOrder(event) {
            event.preventDefault();

            try {
                const userId = localStorage.getItem('userId');
                if (!userId) {
                    throw new Error('User not logged in');
                }

                // Get form data
                const form = event.target;
                const formData = new FormData(form);
                const orderData = {
                    userId: userId,
                    username: formData.get('fullName'),
                    phone: formData.get('phone'),
                    totalAmount: parseFloat(document.getElementById('total').textContent.replace(/[^0-9.-]+/g, '')),
                    status: 'PENDING',
                    shippingAddress: `${formData.get('address')}, ${formData.get('ward')}, ${formData.get('district')}, ${formData.get('province')}`,
                    paymentMethod: formData.get('paymentMethod'),
                    paymentStatus: 'PENDING',
                    note: formData.get('note'),
                    orders: window.cartItems.map(item => ({
                        productId: item.productId,
                        quantity: item.quantity,
                        price: item.price
                    }))
                };

                // Validate form
                if (!orderData.username || !orderData.phone || !orderData.shippingAddress) {
                    showToast('Vui lòng điền đầy đủ thông tin giao hàng', 'error');
                    return;
                }

                // Show loading
                showLoading();

                // Create order
                const order = await OrderService.create(orderData);

                // Clear cart
                await CartService.clear(userId);

                // Hide loading
                hideLoading();

                // Redirect to confirmation page
                window.location.href = `confirmation.html?orderId=${order.id}`;
            } catch (error) {
                console.error('Error creating order:', error);
                hideLoading();
                showToast('Không thể tạo đơn hàng. Vui lòng thử lại sau.', 'error');
            }
        }

        // Load provinces
        async function loadProvinces() {
            try {
                const response = await fetch('https://provinces.open-api.vn/api/?depth=3');
                const provinces = await response.json();

                const provinceSelect = document.getElementById('province');
                const districtSelect = document.getElementById('district');
                const wardSelect = document.getElementById('ward');

                // Populate provinces
                provinceSelect.innerHTML = '<option value="">Chọn tỉnh/thành phố</option>' +
                    provinces.map(p => `<option value="${p.name}">${p.name}</option>`).join('');

                // Handle province change
                provinceSelect.addEventListener('change', function() {
                    const province = provinces.find(p => p.name === this.value);
                    if (province) {
                        // Populate districts
                        districtSelect.innerHTML = '<option value="">Chọn quận/huyện</option>' +
                            province.districts.map(d => `<option value="${d.name}">${d.name}</option>`).join('');
                        districtSelect.disabled = false;

                        // Reset ward
                        wardSelect.innerHTML = '<option value="">Chọn phường/xã</option>';
                        wardSelect.disabled = true;
                    } else {
                        districtSelect.innerHTML = '<option value="">Chọn quận/huyện</option>';
                        districtSelect.disabled = true;
                        wardSelect.innerHTML = '<option value="">Chọn phường/xã</option>';
                        wardSelect.disabled = true;
                    }
                });

                // Handle district change
                districtSelect.addEventListener('change', function() {
                    const province = provinces.find(p => p.name === provinceSelect.value);
                    if (province) {
                        const district = province.districts.find(d => d.name === this.value);
                        if (district) {
                            // Populate wards
                            wardSelect.innerHTML = '<option value="">Chọn phường/xã</option>' +
                                district.wards.map(w => `<option value="${w.name}">${w.name}</option>`).join('');
                            wardSelect.disabled = false;
                        } else {
                            wardSelect.innerHTML = '<option value="">Chọn phường/xã</option>';
                            wardSelect.disabled = true;
                        }
                    }
                });
            } catch (error) {
                console.error('Error loading provinces:', error);
                showToast('Không thể tải danh sách địa chỉ. Vui lòng thử lại sau.', 'error');
            }
        }

        // Select payment method
        function selectPaymentMethod(element, method) {
            // Update radio button
            document.querySelector(`input[name="paymentMethod"][value="${method}"]`).checked = true;
            
            // Update active class
            document.querySelectorAll('.payment-method').forEach(el => {
                el.classList.remove('active');
            });
            element.classList.add('active');
        }
    </script>
</body>
</html>
