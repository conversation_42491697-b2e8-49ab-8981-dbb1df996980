package com.project.codebasespringjpa.dto.product.response;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProductVariantResponse {
    Long id;
    String name;
    BigDecimal price;
    Integer quantity;
    String color;
    String image;
    Map<String, String> attributes;
    LocalDateTime createDate;
    String createBy;
    LocalDateTime updateDate;
    String updateBy;
}
