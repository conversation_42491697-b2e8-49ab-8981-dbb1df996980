package com.project.codebasespringjpa;

import com.project.codebasespringjpa.controller.BlogController;
import com.project.codebasespringjpa.controller.CartController;
import com.project.codebasespringjpa.controller.OrderController;
import com.project.codebasespringjpa.controller.ProductController;
import com.project.codebasespringjpa.dto.blog.response.BlogResponse;
import com.project.codebasespringjpa.dto.product.request.ProductSearch;
import com.project.codebasespringjpa.entity.BlogEntity;
import com.project.codebasespringjpa.entity.CartEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.mapper.BlogMapper;
import com.project.codebasespringjpa.mapper.ProductMapper;
import com.project.codebasespringjpa.repository.IBlogRepository;
import com.project.codebasespringjpa.repository.IOrderRepository;
import com.project.codebasespringjpa.repository.IProductRepository;
import com.project.codebasespringjpa.service.impl.BlogServiceImpl;
import com.project.codebasespringjpa.service.interfaces.IBlogService;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Draff {
    int id;

//    UserEntity
//    CartController
//    OrderController
//    BlogMapper
//    CartEntity
}
