// Common JavaScript functions for Client

// Toggle mobile menu
function toggleMobileMenu() {
    const navList = document.querySelector('.nav-list');
    navList.classList.toggle('show');
}

// Toggle dropdown menu
function toggleDropdown(element) {
    element.classList.toggle('active');
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
}

// Format date
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('vi-VN', options);
}

// Show alert
function showAlert(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) return;

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = message;

    alertContainer.appendChild(alertDiv);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Add to cart
async function addToCart(productId, quantity = 1, variantId = null) {
    if (!isLoggedIn()) {
        // Redirect to login page with return URL
        const currentPath = window.location.pathname + window.location.search;
        window.location.href = '/pages/login/index.html?redirect=' + encodeURIComponent(currentPath);
        return;
    }

    try {
        const userId = localStorage.getItem('userId');
        if (!userId) {
            throw new Error('User ID not found');
        }

        // Get product details to get price
        const product = await apiRequest(`products/${productId}`);
        if (!product) {
            throw new Error('Product not found');
        }

        const cartData = {
            productId: productId,
            quantity: quantity,
            price: product.salePrice || product.price,
            userId: parseInt(userId)
        };

        await apiRequest('carts/create', 'POST', cartData);

        // Update cart count
        updateCartCount();

        // Show success message
        showAlert('Sản phẩm đã được thêm vào giỏ hàng!', 'success');
    } catch (error) {
        console.error('Error adding to cart:', error);
        showAlert('Không thể thêm sản phẩm vào giỏ hàng. Vui lòng thử lại sau.', 'error');
    }
}

// Update cart count
async function updateCartCount() {
    const cartCountElement = document.getElementById('cartCount');
    if (!cartCountElement) return;

    try {
        const userId = localStorage.getItem('userId');
        if (!userId) {
            cartCountElement.textContent = '0';
            return;
        }

        const cartItems = await apiRequest(`carts/user/${userId}`);
        const totalItems = cartItems.reduce((total, item) => total + item.quantity, 0);
        cartCountElement.textContent = totalItems;
    } catch (error) {
        console.error('Error updating cart count:', error);
        cartCountElement.textContent = '0';
    }
}

// Add to wishlist
function addToWishlist(productId) {
    // Get current wishlist from localStorage
    let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

    // Check if product already exists in wishlist
    if (!wishlist.includes(productId)) {
        // Add product to wishlist
        wishlist.push(productId);

        // Save updated wishlist to localStorage
        localStorage.setItem('wishlist', JSON.stringify(wishlist));

        // Show success message
        showAlert('Sản phẩm đã được thêm vào danh sách yêu thích!', 'success');
    } else {
        // Show info message
        showAlert('Sản phẩm đã có trong danh sách yêu thích!', 'info');
    }

    // Update wishlist count
    updateWishlistCount();
}

// Update wishlist count
function updateWishlistCount() {
    const wishlistCountElement = document.getElementById('wishlistCount');
    if (!wishlistCountElement) return;

    const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

    wishlistCountElement.textContent = wishlist.length;
}

// API request helper
async function apiRequest(endpoint, method = 'GET', data = null) {
    try {
        const API_BASE_URL = 'http://localhost:8080';

        // Ensure endpoint starts with proper path
        let url;
        if (endpoint.startsWith('auth/')) {
            url = `${API_BASE_URL}/${endpoint}`;
        } else if (endpoint.startsWith('api/')) {
            url = `${API_BASE_URL}/${endpoint}`;
        } else {
            url = `${API_BASE_URL}/api/${endpoint}`;
        }

        const headers = {
            'Content-Type': 'application/json'
        };

        const token = localStorage.getItem('token');
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        const options = {
            method,
            headers
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(url, options);

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const responseData = await response.json();

        // Handle ApiResponse wrapper from backend
        if (responseData.data !== undefined) {
            return responseData.data;
        }

        return responseData;
    } catch (error) {
        console.error('API request error:', error);
        throw error;
    }
}

// Get data from API
async function fetchData(endpoint) {
    try {
        return await apiRequest(`/api/${endpoint}`);
    } catch (error) {
        console.error(`Error fetching data from ${endpoint}:`, error);
        return null;
    }
}

// Create star rating HTML
function createStarRating(rating) {
    const fullStar = '<i class="fas fa-star"></i>';
    const halfStar = '<i class="fas fa-star-half-alt"></i>';
    const emptyStar = '<i class="far fa-star"></i>';

    let stars = '';

    // Add full stars
    for (let i = 1; i <= Math.floor(rating); i++) {
        stars += fullStar;
    }

    // Add half star if needed
    if (rating % 1 !== 0) {
        stars += halfStar;
    }

    // Add empty stars
    for (let i = Math.ceil(rating); i < 5; i++) {
        stars += emptyStar;
    }

    return `<div class="product-rating">${stars} <span>(${rating})</span></div>`;
}

// Generate product card HTML
function generateProductCard(product) {
    const discountPercent = product.salePrice && product.price > product.salePrice
        ? Math.round((1 - product.salePrice / product.price) * 100)
        : 0;

    const discountBadge = discountPercent > 0
        ? `<span class="product-badge">-${discountPercent}%</span>`
        : '';

    const priceHTML = discountPercent > 0
        ? `<div class="product-price">
            <span class="current-price">${formatCurrency(product.salePrice)}</span>
            <span class="old-price">${formatCurrency(product.price)}</span>
           </div>`
        : `<div class="product-price">
            <span class="current-price">${formatCurrency(product.price)}</span>
           </div>`;

    return `
        <div class="product-card">
            <div class="product-image">
                ${discountBadge}
                <a href="../product/detail.html?id=${product.id}">
                    <img src="${product.thumbnail}" alt="${product.name}">
                </a>
                <div class="product-actions">
                    <button class="btn-action" onclick="addToWishlist(${product.id})">
                        <i class="far fa-heart"></i>
                    </button>
                    <button class="btn-action" onclick="addToCart(${product.id})">
                        <i class="fas fa-shopping-cart"></i>
                    </button>
                    <button class="btn-action" onclick="quickView(${product.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
            <div class="product-info">
                <h3 class="product-title">
                    <a href="../product/detail.html?id=${product.id}">${product.name}</a>
                </h3>
                ${createStarRating(product.averageRating || 0)}
                ${priceHTML}
            </div>
        </div>
    `;
}

// Check if user is logged in
function isLoggedIn() {
    return localStorage.getItem('token') !== null;
}

// Show loading spinner
function showLoading() {
    // Create loading spinner if it doesn't exist
    if (!document.getElementById('loadingSpinner')) {
        const spinner = document.createElement('div');
        spinner.id = 'loadingSpinner';
        spinner.className = 'loading-spinner';
        spinner.innerHTML = '<div class="spinner"></div>';
        document.body.appendChild(spinner);
    }

    // Show spinner
    document.getElementById('loadingSpinner').style.display = 'flex';
}

// Hide loading spinner
function hideLoading() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.style.display = 'none';
    }
}

// Show toast notification
function showToast(message, type = 'success') {
    // Create toast container if it doesn't exist
    if (!document.querySelector('.toast-container')) {
        const toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }

    // Create toast
    const toast = document.createElement('div');
    toast.className = `toast show bg-${type} text-white`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="toast-header bg-${type} text-white">
            <strong class="me-auto">${type === 'success' ? 'Thành công' : type === 'danger' ? 'Lỗi' : 'Thông báo'}</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;

    // Add toast to container
    document.querySelector('.toast-container').appendChild(toast);

    // Auto dismiss after 3 seconds
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// Get URL parameter
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

// Load header and footer
async function loadHeaderAndFooter() {
    try {
        // Load header
        const headerContainer = document.getElementById('header-container');
        if (headerContainer) {
            const headerResponse = await fetch('/layout/header/header.html');
            const headerHtml = await headerResponse.text();
            headerContainer.innerHTML = headerHtml;

            // Update login/register buttons based on login status
            updateAuthUI();
        }

        // Load footer
        const footerContainer = document.getElementById('footer-container');
        if (footerContainer) {
            const footerResponse = await fetch('/layout/footer/footer.html');
            const footerHtml = await footerResponse.text();
            footerContainer.innerHTML = footerHtml;
        }
    } catch (error) {
        console.error('Error loading header or footer:', error);
    }
}

// Update authentication UI
function updateAuthUI() {
    const loginBtn = document.getElementById('login-btn');
    const registerBtn = document.getElementById('register-btn');
    const userDropdown = document.getElementById('user-dropdown');
    if (!loginBtn || !registerBtn || !userDropdown) return;
    if (isLoggedIn()) {
        loginBtn.classList.add('d-none');
        registerBtn.classList.add('d-none');
        userDropdown.classList.remove('d-none');
        // Lấy tên user từ localStorage
        const fullName = localStorage.getItem('fullName');
        if (fullName) {
            document.getElementById('user-name').textContent = fullName;
        }
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function(e) {
                e.preventDefault();
                logout();
            });
        }
    } else {
        loginBtn.classList.remove('d-none');
        registerBtn.classList.remove('d-none');
        userDropdown.classList.add('d-none');
    }
}

// Logout function
function logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('role');
    localStorage.removeItem('email');
    localStorage.removeItem('fullName');
    showToast('Đăng xuất thành công');
    setTimeout(() => {
        window.location.href = '/index.html';
    }, 1000);
}

// Document ready
document.addEventListener('DOMContentLoaded', function() {
    // Load header and footer
    loadHeaderAndFooter();

    // Update cart and wishlist counts
    updateCartCount();
    updateWishlistCount();

    // Initialize back to top button
    const backToTopButton = document.querySelector('.back-to-top');
    if (backToTopButton) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('show');
            } else {
                backToTopButton.classList.remove('show');
            }
        });

        backToTopButton.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }
});
