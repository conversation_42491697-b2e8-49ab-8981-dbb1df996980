package com.project.codebasespringjpa.controller;

import com.project.codebasespringjpa.dto.supplier.request.SupplierCreateRequest;
import com.project.codebasespringjpa.dto.supplier.request.SupplierUpdateRequest;
import com.project.codebasespringjpa.dto.supplier.response.SupplierResponse;
import com.project.codebasespringjpa.service.interfaces.ISupplierService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/suppliers")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SupplierController {
    ISupplierService supplierService;

    @GetMapping
    public ResponseEntity<List<SupplierResponse>> findAll(@RequestParam(name = "keyword", required = false) String keyword) {
        return ResponseEntity.ok(supplierService.findAll(keyword));
    }

    @GetMapping("/{id}")
    public ResponseEntity<SupplierResponse> findById(@PathVariable Long id) {
        return ResponseEntity.ok(supplierService.findById(id));
    }

    @GetMapping("/name/{name}")
    public ResponseEntity<SupplierResponse> findByName(@PathVariable String name) {
        return ResponseEntity.ok(supplierService.findByName(name));
    }

    @GetMapping("/email/{email}")
    public ResponseEntity<SupplierResponse> findByEmail(@PathVariable String email) {
        return ResponseEntity.ok(supplierService.findByEmail(email));
    }

    @PostMapping
    public ResponseEntity<SupplierResponse> create(@RequestBody SupplierCreateRequest request) {
        return new ResponseEntity<>(supplierService.create(request), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<SupplierResponse> update(@PathVariable Long id, @RequestBody SupplierUpdateRequest request) {
        return ResponseEntity.ok(supplierService.update(id, request));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        supplierService.delete(id);
        return ResponseEntity.noContent().build();
    }
}
