package com.project.codebasespringjpa.service.impl;

import com.project.codebasespringjpa.dto.blog.request.BlogRequest;
import com.project.codebasespringjpa.dto.blog.response.BlogResponse;
import com.project.codebasespringjpa.entity.BlogEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.exception.AppException;
import com.project.codebasespringjpa.exception.ErrorCode;
import com.project.codebasespringjpa.mapper.BlogMapper;
import com.project.codebasespringjpa.repository.IBlogRepository;
import com.project.codebasespringjpa.repository.IUserRepository;
import com.project.codebasespringjpa.service.interfaces.IBlogService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class BlogServiceImpl implements IBlogService {
    IBlogRepository blogRepository;
    IUserRepository userRepository;
    BlogMapper blogMapper;


    @Override
    public Page<BlogResponse> findAll(String keyword, Pageable pageable) {
        Page<BlogEntity> blogs = blogRepository.findAll(keyword, pageable);
        return blogs.map(it -> blogMapper.toResponse(it));
    }

    @Override
    public BlogResponse findById(Long id) {
        BlogEntity blog = blogRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.BLOG_NOT_FOUND));
        return blogMapper.toResponse(blog);
    }


    @Override
    public BlogResponse create(BlogRequest request) {

        return blogMapper.toResponse(blogRepository.save(blogMapper.toEntity(request)));
    }

    @Override
    public BlogResponse update(Long id, BlogRequest request) {
        BlogEntity blog = blogRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.BLOG_NOT_FOUND));

        blog.setTitle(request.getTitle());
        blog.setContent(request.getContent());
        blog.setImage(request.getImage());
        blogRepository.save(blog);
        return blogMapper.toResponse(blog);
    }

    @Override
    public void delete(Long id) {
        BlogEntity blog = blogRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.BLOG_NOT_FOUND));

        blog.setIsDelete(true);
        blogRepository.save(blog);
    }

}
