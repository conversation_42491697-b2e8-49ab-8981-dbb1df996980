package com.project.codebasespringjpa.service.impl;

import com.project.codebasespringjpa.dto.order.request.OrderRequest;
import com.project.codebasespringjpa.dto.order.request.OrderDetailRequest;
import com.project.codebasespringjpa.dto.order.request.OrderSearch;
import com.project.codebasespringjpa.dto.order.request.StatusRequest;
import com.project.codebasespringjpa.dto.order.response.OrderResponse;
import com.project.codebasespringjpa.entity.OrderDetailEntity;
import com.project.codebasespringjpa.entity.OrderEntity;
import com.project.codebasespringjpa.entity.ProductEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.enums.OrderStatusEnum;
import com.project.codebasespringjpa.exception.AppException;
import com.project.codebasespringjpa.exception.ErrorCode;
import com.project.codebasespringjpa.mapper.OrderDetailMapper;
import com.project.codebasespringjpa.mapper.OrderMapper;
import com.project.codebasespringjpa.repository.ICartRepository;
import com.project.codebasespringjpa.repository.IOrderDetailRepository;
import com.project.codebasespringjpa.repository.IOrderRepository;
import com.project.codebasespringjpa.repository.IProductRepository;
import com.project.codebasespringjpa.repository.IUserRepository;
import com.project.codebasespringjpa.service.interfaces.ICartService;
import com.project.codebasespringjpa.service.interfaces.IOrderService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Transactional
public class OrderServiceImpl implements IOrderService {
    IOrderRepository orderRepository;
    IOrderDetailRepository orderDetailRepository;
    IUserRepository userRepository;
    IProductRepository productRepository;
    ICartRepository cartRepository;
    OrderMapper orderMapper;
    OrderDetailMapper orderDetailMapper;
    ICartService cartService;

    @Override
    public Page<OrderResponse> findAll(OrderSearch orderSearch, Pageable pageable) {
        return orderRepository.findAll(orderSearch.getUserId(),orderSearch.getStatus(),
                        orderSearch.getKeyword(), orderSearch.getStartDate(), orderSearch.getEndDate(),
                        pageable)
                .map(it -> orderMapper.toResponse(it));
    }

    @Override
    public OrderResponse findById(Long id) {
        OrderEntity order = orderRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.ORDER_NOT_FOUND));
        return orderMapper.toResponse(order);
    }


    @Override
    public Long countByStatus(String status) {
        return orderRepository.countByStatus(status);
    }

    @Override
    public Double getTotalRevenue() {
        return orderRepository.getTotalRevenue();
    }

    @Override
    public Double getRevenueBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return orderRepository.getRevenueBetween(startDate, endDate);
    }

    @Override
    public OrderResponse create(OrderRequest request) {
        OrderEntity order = orderMapper.toEntity(request);
        orderRepository.save(order);

        for (OrderDetailRequest orderDetailRequest: request.getOrders()){
            OrderDetailEntity orderDetail = orderDetailMapper.toEntity(orderDetailRequest);
            orderDetail.setOrder(order);
            orderDetailRepository.save(orderDetail);
        }

        return orderMapper.toResponse(order);
    }

    @Override
    public OrderResponse updateStatus(Long id, String status) {
        OrderEntity order = orderRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.ORDER_NOT_FOUND));

        // Kiểm tra trạng thái hợp lệ
        order.setStatus(status);
        OrderEntity updatedOrder = orderRepository.save(order);

        return orderMapper.toResponse(updatedOrder);
    }

    @Override
    public void delete(Long id) {
        OrderEntity order = orderRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.ORDER_NOT_FOUND));

        // Xóa chi tiết đơn hàng
        orderDetailRepository.deleteByOrder(order);

        // Xóa đơn hàng
        orderRepository.delete(order);
    }



    private void updateOrderTotal(OrderEntity order) {
        List<OrderDetailEntity> orderDetails = orderDetailRepository.findByOrder(order);


        orderRepository.save(order);
    }

    private void updateProductQuantity(ProductEntity product, int quantity) {


        productRepository.save(product);
    }
}
