<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON> t<PERSON><PERSON> - <PERSON> Shop</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.min.css">
    <style>
        .account-sidebar {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .account-sidebar-title {
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .account-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .account-menu-item {
            margin-bottom: 10px;
        }
        
        .account-menu-link {
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 5px;
            color: #333;
            transition: all 0.3s ease;
        }
        
        .account-menu-link:hover {
            background-color: #e9ecef;
        }
        
        .account-menu-link.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .account-menu-icon {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .account-content {
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        }
        
        .account-content-title {
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 20px;
        }
        
        .profile-info {
            margin-bottom: 30px;
        }
        
        .profile-info-item {
            margin-bottom: 15px;
        }
        
        .profile-info-label {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .profile-info-value {
            color: #6c757d;
        }
        
        .address-card {
            border: 1px solid #eee;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            position: relative;
        }
        
        .address-card-default {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
        }
        
        .address-card-title {
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .address-card-content {
            color: #6c757d;
            margin-bottom: 15px;
        }
        
        .address-card-actions {
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Main Content -->
    <main class="container my-5">
        <h1 class="mb-4">Tài khoản của tôi</h1>
        
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../../index.html">Trang chủ</a></li>
                <li class="breadcrumb-item active" aria-current="page">Tài khoản của tôi</li>
            </ol>
        </nav>
        
        <div class="row">
            <!-- Account Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="account-sidebar">
                    <h3 class="account-sidebar-title">Tài khoản</h3>
                    <ul class="account-menu">
                        <li class="account-menu-item">
                            <a href="index.html" class="account-menu-link active">
                                <span class="account-menu-icon"><i class="fas fa-user"></i></span>
                                Thông tin tài khoản
                            </a>
                        </li>
                        <li class="account-menu-item">
                            <a href="orders.html" class="account-menu-link">
                                <span class="account-menu-icon"><i class="fas fa-shopping-bag"></i></span>
                                Đơn hàng của tôi
                            </a>
                        </li>
                        <li class="account-menu-item">
                            <a href="addresses.html" class="account-menu-link">
                                <span class="account-menu-icon"><i class="fas fa-map-marker-alt"></i></span>
                                Sổ địa chỉ
                            </a>
                        </li>
                        <li class="account-menu-item">
                            <a href="wishlist.html" class="account-menu-link">
                                <span class="account-menu-icon"><i class="fas fa-heart"></i></span>
                                Sản phẩm yêu thích
                            </a>
                        </li>
                        <li class="account-menu-item">
                            <a href="change-password.html" class="account-menu-link">
                                <span class="account-menu-icon"><i class="fas fa-lock"></i></span>
                                Đổi mật khẩu
                            </a>
                        </li>
                        <li class="account-menu-item">
                            <a href="#" class="account-menu-link" id="logout-link">
                                <span class="account-menu-icon"><i class="fas fa-sign-out-alt"></i></span>
                                Đăng xuất
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Account Content -->
            <div class="col-lg-9">
                <div class="account-content">
                    <h2 class="account-content-title">Thông tin tài khoản</h2>
                    
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <img src="../../assets/image/user-avatar.jpg" alt="Avatar" class="profile-avatar" id="user-avatar">
                            <div class="mt-3">
                                <button class="btn btn-outline-primary btn-sm" id="change-avatar-btn">
                                    <i class="fas fa-camera me-2"></i> Thay đổi ảnh đại diện
                                </button>
                                <input type="file" id="avatar-upload" class="d-none" accept="image/*">
                            </div>
                        </div>
                        
                        <div class="col-md-8">
                            <div class="profile-info">
                                <div class="profile-info-item">
                                    <div class="profile-info-label">Họ và tên</div>
                                    <div class="profile-info-value" id="user-name">Nguyễn Văn A</div>
                                </div>
                                <div class="profile-info-item">
                                    <div class="profile-info-label">Email</div>
                                    <div class="profile-info-value" id="user-email"><EMAIL></div>
                                </div>
                                <div class="profile-info-item">
                                    <div class="profile-info-label">Số điện thoại</div>
                                    <div class="profile-info-value" id="user-phone">0123456789</div>
                                </div>
                                <div class="profile-info-item">
                                    <div class="profile-info-label">Ngày tham gia</div>
                                    <div class="profile-info-value" id="user-join-date">01/01/2023</div>
                                </div>
                            </div>
                            
                            <button class="btn btn-primary" id="edit-profile-btn">
                                <i class="fas fa-edit me-2"></i> Chỉnh sửa thông tin
                            </button>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <h3 class="mb-3">Địa chỉ mặc định</h3>
                    <div class="row" id="addresses-container">
                        <!-- Addresses will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Edit Profile Modal -->
    <div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editProfileModalLabel">Chỉnh sửa thông tin</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editProfileForm">
                        <div class="mb-3">
                            <label for="editName" class="form-label">Họ và tên</label>
                            <input type="text" class="form-control" id="editName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="editEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="editPhone" class="form-label">Số điện thoại</label>
                            <input type="tel" class="form-control" id="editPhone" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="button" class="btn btn-primary" id="saveProfileBtn">Lưu thay đổi</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.all.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/api.js"></script>
    <script src="../../assets/js/common.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            if (!isLoggedIn()) {
                // Redirect to login page
                Swal.fire({
                    title: 'Yêu cầu đăng nhập',
                    text: 'Vui lòng đăng nhập để xem thông tin tài khoản',
                    icon: 'warning',
                    confirmButtonText: 'Đăng nhập'
                }).then(() => {
                    window.location.href = '../login/index.html?redirect=' + encodeURIComponent(window.location.href);
                });
                return;
            }
            
            // Load user profile
            loadUserProfile();
            
            // Load user addresses
            loadUserAddresses();
            
            // Handle edit profile button
            document.getElementById('edit-profile-btn').addEventListener('click', function() {
                // Populate form with current user data
                const userName = document.getElementById('user-name').textContent;
                const userEmail = document.getElementById('user-email').textContent;
                const userPhone = document.getElementById('user-phone').textContent;
                
                document.getElementById('editName').value = userName;
                document.getElementById('editEmail').value = userEmail;
                document.getElementById('editPhone').value = userPhone;
                
                // Show modal
                const editProfileModal = new bootstrap.Modal(document.getElementById('editProfileModal'));
                editProfileModal.show();
            });
            
            // Handle save profile button
            document.getElementById('saveProfileBtn').addEventListener('click', function() {
                updateUserProfile();
            });
            
            // Handle change avatar button
            document.getElementById('change-avatar-btn').addEventListener('click', function() {
                document.getElementById('avatar-upload').click();
            });
            
            // Handle avatar upload
            document.getElementById('avatar-upload').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    uploadAvatar(e.target.files[0]);
                }
            });
            
            // Handle logout link
            document.getElementById('logout-link').addEventListener('click', function(e) {
                e.preventDefault();
                logout();
            });
        });
        
        // Load user profile
        function loadUserProfile() {
            // In a real application, you would call your API
            // For now, we'll simulate an API call with mock data
            setTimeout(() => {
                // Mock user data
                const userData = {
                    name: 'Nguyễn Văn A',
                    email: '<EMAIL>',
                    phone: '0123456789',
                    joinDate: '2023-01-01',
                    avatar: '../../assets/image/user-avatar.jpg'
                };
                
                // Update profile info
                document.getElementById('user-name').textContent = userData.name;
                document.getElementById('user-email').textContent = userData.email;
                document.getElementById('user-phone').textContent = userData.phone;
                document.getElementById('user-join-date').textContent = formatDate(userData.joinDate);
                document.getElementById('user-avatar').src = userData.avatar;
            }, 500);
        }
        
        // Load user addresses
        function loadUserAddresses() {
            const addressesContainer = document.getElementById('addresses-container');
            
            // In a real application, you would call your API
            // For now, we'll simulate an API call with mock data
            setTimeout(() => {
                // Mock addresses data
                const addresses = [
                    {
                        id: 1,
                        name: 'Nguyễn Văn A',
                        phone: '0123456789',
                        address: '123 Đường ABC, Phường XYZ, Quận 1',
                        city: 'TP. Hồ Chí Minh',
                        isDefault: true
                    },
                    {
                        id: 2,
                        name: 'Nguyễn Văn A',
                        phone: '0987654321',
                        address: '456 Đường DEF, Phường UVW, Quận 2',
                        city: 'TP. Hồ Chí Minh',
                        isDefault: false
                    }
                ];
                
                // Generate addresses HTML
                addressesContainer.innerHTML = '';
                
                addresses.forEach(address => {
                    const addressElement = document.createElement('div');
                    addressElement.className = 'col-md-6 mb-3';
                    addressElement.innerHTML = `
                        <div class="address-card">
                            ${address.isDefault ? '<div class="address-card-default">Mặc định</div>' : ''}
                            <h5 class="address-card-title">${address.name}</h5>
                            <div class="address-card-content">
                                <p>Địa chỉ: ${address.address}, ${address.city}</p>
                                <p>Điện thoại: ${address.phone}</p>
                            </div>
                            <div class="address-card-actions">
                                <button class="btn btn-sm btn-outline-primary" onclick="editAddress(${address.id})">
                                    <i class="fas fa-edit"></i> Sửa
                                </button>
                                ${!address.isDefault ? `
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteAddress(${address.id})">
                                        <i class="fas fa-trash-alt"></i> Xóa
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="setDefaultAddress(${address.id})">
                                        <i class="fas fa-check"></i> Đặt làm mặc định
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    `;
                    
                    addressesContainer.appendChild(addressElement);
                });
                
                // Add "Add new address" card
                const addAddressElement = document.createElement('div');
                addAddressElement.className = 'col-md-6 mb-3';
                addAddressElement.innerHTML = `
                    <div class="address-card d-flex align-items-center justify-content-center" style="min-height: 200px; cursor: pointer;" onclick="addNewAddress()">
                        <div class="text-center">
                            <i class="fas fa-plus-circle fa-3x mb-3 text-muted"></i>
                            <h5>Thêm địa chỉ mới</h5>
                        </div>
                    </div>
                `;
                
                addressesContainer.appendChild(addAddressElement);
            }, 500);
        }
        
        // Update user profile
        function updateUserProfile() {
            const name = document.getElementById('editName').value;
            const email = document.getElementById('editEmail').value;
            const phone = document.getElementById('editPhone').value;
            
            // Validate form
            if (!name || !email || !phone) {
                Swal.fire({
                    title: 'Lỗi',
                    text: 'Vui lòng điền đầy đủ thông tin',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }
            
            // Show loading
            showLoading();
            
            // In a real application, you would call your API
            // For now, we'll simulate an API call with a delay
            setTimeout(() => {
                hideLoading();
                
                // Update UI
                document.getElementById('user-name').textContent = name;
                document.getElementById('user-email').textContent = email;
                document.getElementById('user-phone').textContent = phone;
                
                // Hide modal
                const editProfileModal = bootstrap.Modal.getInstance(document.getElementById('editProfileModal'));
                editProfileModal.hide();
                
                // Show success message
                showToast('Thông tin tài khoản đã được cập nhật');
            }, 1000);
        }
        
        // Upload avatar
        function uploadAvatar(file) {
            // Show loading
            showLoading();
            
            // In a real application, you would upload the file to your server
            // For now, we'll simulate an API call with a delay
            setTimeout(() => {
                hideLoading();
                
                // Create a URL for the file
                const fileUrl = URL.createObjectURL(file);
                
                // Update avatar
                document.getElementById('user-avatar').src = fileUrl;
                
                // Show success message
                showToast('Ảnh đại diện đã được cập nhật');
            }, 1000);
        }
        
        // Edit address
        function editAddress(addressId) {
            // In a real application, you would get the address details and show an edit form
            // For now, we'll just show a message
            Swal.fire({
                title: 'Chỉnh sửa địa chỉ',
                text: 'Chức năng này sẽ được triển khai sau',
                icon: 'info',
                confirmButtonText: 'OK'
            });
        }
        
        // Delete address
        function deleteAddress(addressId) {
            Swal.fire({
                title: 'Xác nhận',
                text: 'Bạn có chắc chắn muốn xóa địa chỉ này?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Xóa',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    // In a real application, you would call your API
                    // For now, we'll just reload the addresses
                    showLoading();
                    
                    setTimeout(() => {
                        hideLoading();
                        loadUserAddresses();
                        showToast('Địa chỉ đã được xóa');
                    }, 1000);
                }
            });
        }
        
        // Set default address
        function setDefaultAddress(addressId) {
            // In a real application, you would call your API
            // For now, we'll just reload the addresses
            showLoading();
            
            setTimeout(() => {
                hideLoading();
                loadUserAddresses();
                showToast('Đã đặt địa chỉ làm mặc định');
            }, 1000);
        }
        
        // Add new address
        function addNewAddress() {
            // In a real application, you would show a form to add a new address
            // For now, we'll just show a message
            Swal.fire({
                title: 'Thêm địa chỉ mới',
                text: 'Chức năng này sẽ được triển khai sau',
                icon: 'info',
                confirmButtonText: 'OK'
            });
        }
        
        // Logout
        function logout() {
            Swal.fire({
                title: 'Xác nhận',
                text: 'Bạn có chắc chắn muốn đăng xuất?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Đăng xuất',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Clear local storage
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    
                    // Redirect to home page
                    window.location.href = '../../index.html';
                }
            });
        }
    </script>
</body>
</html>
