// Authentication Service for Client

// Authentication API
const AuthService = {
    login: async (username, password) => {
        try {
            const response = await apiRequest('auth/login', 'POST', { username, password });
            if (response.token) {
                setAuthToken(response.token);
                // Store user info
                localStorage.setItem('userId', response.userId);
                localStorage.setItem('role', response.role);
                localStorage.setItem('username', response.username);
            }
            return response;
        } catch (error) {
            throw error;
        }
    },

    register: async (userData) => {
        return await apiRequest('auth/register', 'POST', userData);
    },

    logout: () => {
        removeAuthToken();
        localStorage.removeItem('userId');
        localStorage.removeItem('role');
        localStorage.removeItem('username');
        window.location.href = '../../pages/login/index.html';
    },

    isAuthenticated: () => {
        return !!getAuthToken();
    },

    getCurrentUser: async () => {
        if (!getAuthToken()) {
            return null;
        }

        try {
            return await apiRequest('users/profile');
        } catch (error) {
            return null;
        }
    }
};
