package com.project.codebasespringjpa.dto.supplier.response;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SupplierResponse {
    Long id;
    String name;
    String email;
    String phone;
    String address;
    String description;
    Boolean status;
    LocalDateTime createDate;
    String createBy;
    LocalDateTime updateDate;
    String updateBy;
}
