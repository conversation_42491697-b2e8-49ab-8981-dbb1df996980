.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  padding: 60px 0 0; /* Tăng padding-top để phù hợp với header mới */
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
  overflow-y: auto; /* <PERSON> phép cuộn nếu menu dài */
}

.sidebar-sticky {
  position: relative;
  top: 0;
  height: calc(100vh - 60px); /* <PERSON><PERSON><PERSON><PERSON> chỉnh theo chiều cao header mới */
  padding-top: .5rem;
  overflow-x: hidden;
  overflow-y: auto;
}

.sidebar .nav-link {
  font-weight: 500;
  color: #333;
  padding: .75rem 1.25rem;
  border-radius: 0.25rem;
  margin: 0.2rem 0.5rem;
  transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
  color: #007bff;
  background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
  color: #007bff;
  background-color: rgba(0, 123, 255, 0.15);
  font-weight: 600;
}

.sidebar .nav-link i {
  margin-right: 8px;
}

.sidebar-heading {
  font-size: .75rem;
  text-transform: uppercase;
  font-weight: 600;
  color: #6c757d;
  padding: 1rem 1.5rem 0.5rem;
  letter-spacing: 0.5px;
}
