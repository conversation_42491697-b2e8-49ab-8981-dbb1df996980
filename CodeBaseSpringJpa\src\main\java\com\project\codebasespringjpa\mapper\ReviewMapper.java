package com.project.codebasespringjpa.mapper;

import com.project.codebasespringjpa.dto.review.request.ReviewCreateRequest;
import com.project.codebasespringjpa.dto.review.request.ReviewUpdateRequest;
import com.project.codebasespringjpa.dto.review.response.ReviewResponse;
import com.project.codebasespringjpa.entity.ProductEntity;
import com.project.codebasespringjpa.entity.ReviewEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.util.ImageUtil;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class ReviewMapper {

    /**
     * <PERSON>yển đổi từ ReviewEntity sang ReviewResponse
     */
    public ReviewResponse toResponse(ReviewEntity entity) {
        if (entity == null) {
            return null;
        }

        return ReviewResponse.builder()
                .id(entity.getId())
                .productId(entity.getProduct() != null ? entity.getProduct().getId() : null)
                .productName(entity.getProduct() != null ? entity.getProduct().getName() : null)
                .userId(entity.getUser() != null ? entity.getUser().getId() : null)
                .userName(entity.getUser() != null ? entity.getUser().getFullname() : null)
                .userEmail(entity.getUser() != null ? entity.getUser().getEmail() : null)
                .rating(entity.getRating())
                .comment(entity.getComment())
                .reviewDate(entity.getReviewDate())
                .status(entity.getStatus())
                .createDate(entity.getCreateDate())
                .createBy(entity.getCreateBy())
                .updateDate(entity.getUpdateDate())
                .updateBy(entity.getUpdateBy())
                .build();
    }

    /**
     * Chuyển đổi từ ReviewCreateRequest sang ReviewEntity
     */
    public ReviewEntity toEntity(ReviewCreateRequest request, ProductEntity product, UserEntity user) {
        if (request == null) {
            return null;
        }

        return ReviewEntity.builder()
                .product(product)
                .user(user)
                .rating(request.getRating())
                .comment(request.getComment())
                .reviewDate(LocalDateTime.now())
                .status(true)
                .build();
    }

    /**
     * Cập nhật ReviewEntity từ ReviewUpdateRequest
     */
    public void updateEntityFromRequest(ReviewEntity entity, ReviewUpdateRequest request) {
        if (entity == null || request == null) {
            return;
        }

        if (request.getRating() != null) {
            entity.setRating(request.getRating());
        }

        if (request.getComment() != null) {
            entity.setComment(request.getComment());
        }

        if (request.getStatus() != null) {
            entity.setStatus(request.getStatus());
        }
    }

    /**
     * Chuyển đổi danh sách ReviewEntity sang danh sách ReviewResponse
     */
    public List<ReviewResponse> toResponseList(List<ReviewEntity> entities) {
        if (entities == null) {
            return new ArrayList<>();
        }

        return entities.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }
}
