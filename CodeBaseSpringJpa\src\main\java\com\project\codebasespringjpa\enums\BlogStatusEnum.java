package com.project.codebasespringjpa.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum BlogStatusEnum {
    DRAFT,
    PUBLISHED;

    public static List<String> statusList() {
        List<String> statusList = Arrays.stream(BlogStatusEnum.values())
                .map(Enum::name)
                .collect(Collectors.toList());

        return statusList;
    }
}
