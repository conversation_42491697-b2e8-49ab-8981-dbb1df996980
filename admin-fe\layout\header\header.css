.navbar-brand {
  font-weight: bold;
  font-size: 1.2rem;
}

.bg-gradient-primary {
  background: linear-gradient(90deg, #3a7bd5, #00d2ff);
}

.form-control-dark {
  color: #fff;
  background-color: rgba(255, 255, 255, .1);
  border-color: rgba(255, 255, 255, .1);
  transition: all 0.3s ease;
}

.form-control-dark:focus {
  border-color: transparent;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);
  background-color: rgba(255, 255, 255, .2);
}

.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, .8);
  transition: all 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
  color: #fff;
}

.dropdown-menu {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
}

.dropdown-item {
  padding: 0.5rem 1.5rem;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item i {
  color: #6c757d;
}

.header-spacer {
  height: 56px; /* <PERSON>ều cao của header */
}

@media (min-width: 768px) {
  .header-spacer {
    height: 60px;
  }
}
