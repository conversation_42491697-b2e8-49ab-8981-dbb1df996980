package com.project.codebasespringjpa.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum DiscountTypeEnum {
    PERCENTAGE,
    FIXED_AMOUNT;

    public static List<String> typeList() {
        List<String> typeList = Arrays.stream(DiscountTypeEnum.values())
                .map(Enum::name)
                .collect(Collectors.toList());

        return typeList;
    }
}
