package com.project.codebasespringjpa.service.interfaces;

import com.project.codebasespringjpa.dto.review.request.ReviewCreateRequest;
import com.project.codebasespringjpa.dto.review.request.ReviewUpdateRequest;
import com.project.codebasespringjpa.dto.review.response.ReviewResponse;

import java.util.List;

public interface IReviewService {
    List<ReviewResponse> findAll();
    ReviewResponse findById(Long id);
    List<ReviewResponse> findByProductId(Long productId);
    List<ReviewResponse> findByUserId(Long userId);
    List<ReviewResponse> findByStatus(Boolean status);
    Double getAverageRatingByProduct(Long productId);
    ReviewResponse create(ReviewCreateRequest request);
    ReviewResponse update(Long id, ReviewUpdateRequest request);
    void delete(Long id);
}
