document.addEventListener('DOMContentLoaded', function() {
  // Ki<PERSON>m tra đăng nhập
  if (!checkAuth()) {
    return;
  }

  // Load layout components
  loadLayoutComponents();

  // Khởi tạo biến toàn cục
  let currentPage = 0;
  let pageSize = 10;
  let totalPages = 0;
  let categoryStockChart, categoryValueChart;

  // Load dữ liệu ban đầu
  loadCategories();
  loadSuppliers();
  loadInventoryData();

  // Xử lý sự kiện
  setupEventListeners();

  // Hàm load layout components
  function loadLayoutComponents() {
    // Load header
    fetch('../../layout/header/header.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('header-container').innerHTML = html;

        // Load header script after HTML is inserted
        const headerScript = document.createElement('script');
        headerScript.src = '../../layout/header/header.js';
        document.body.appendChild(headerScript);
      });

    // Load sidebar
    fetch('../../layout/sidebar/sidebar.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('sidebar-container').innerHTML = html;

        // Load sidebar script after HTML is inserted
        const sidebarScript = document.createElement('script');
        sidebarScript.src = '../../layout/sidebar/sidebar.js';
        document.body.appendChild(sidebarScript);
      });

    // Load footer
    fetch('../../layout/footer/footer.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('footer-container').innerHTML = html;
      });
  }

  // Hàm load danh mục
  function loadCategories() {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    axios.get('http://localhost:8080/api/categories')
      .then(response => {
        const categories = response.data;
        const filterCategorySelect = document.getElementById('filterCategory');

        // Xóa options cũ
        filterCategorySelect.innerHTML = '<option value="">Tất cả danh mục</option>';

        // Thêm options mới
        categories.forEach(category => {
          const option = document.createElement('option');
          option.value = category.id;
          option.textContent = category.name;
          filterCategorySelect.appendChild(option);
        });
      })
      .catch(error => {
        handleApiError(error);
      });
  }

  // Hàm load nhà cung cấp
  function loadSuppliers() {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    axios.get('http://localhost:8080/api/suppliers')
      .then(response => {
        const suppliers = response.data;
        const filterSupplierSelect = document.getElementById('filterSupplier');

        // Xóa options cũ
        filterSupplierSelect.innerHTML = '<option value="">Tất cả nhà cung cấp</option>';

        // Thêm options mới
        suppliers.forEach(supplier => {
          const option = document.createElement('option');
          option.value = supplier.id;
          option.textContent = supplier.name;
          filterSupplierSelect.appendChild(option);
        });
      })
      .catch(error => {
        handleApiError(error);
      });
  }

  // Hàm load dữ liệu tồn kho
  function loadInventoryData() {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    const categoryId = document.getElementById('filterCategory').value;
    const supplierId = document.getElementById('filterSupplier').value;
    const stockStatus = document.getElementById('filterStock').value;

    // Hiển thị loading
    document.getElementById('inventoryTable').innerHTML = `
      <tr>
        <td colspan="9" class="text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Đang tải...</span>
          </div>
        </td>
      </tr>
    `;

    // Xây dựng URL với các tham số lọc
    let url = `http://localhost:8080/api/reports/inventory?page=${currentPage}&size=${pageSize}`;

    if (categoryId) {
      url += `&categoryId=${categoryId}`;
    }

    if (supplierId) {
      url += `&supplierId=${supplierId}`;
    }

    if (stockStatus) {
      url += `&stockStatus=${stockStatus}`;
    }

    axios.get(url)
      .then(response => {
        const inventoryData = response.data;

        // Hiển thị tổng quan
        displayOverview(inventoryData.overview);

        // Hiển thị biểu đồ tồn kho theo danh mục
        displayCategoryStockChart(inventoryData.stockByCategory);

        // Hiển thị biểu đồ giá trị tồn kho theo danh mục
        displayCategoryValueChart(inventoryData.valueByCategory);

        // Hiển thị bảng chi tiết
        displayInventoryTable(inventoryData.products.content);

        // Cập nhật thông tin phân trang
        document.getElementById('displayedProducts').textContent = inventoryData.products.numberOfElements;
        document.getElementById('totalFilteredProducts').textContent = inventoryData.products.totalElements;
        totalPages = inventoryData.products.totalPages;

        // Hiển thị phân trang
        displayPagination(currentPage, totalPages);
      })
      .catch(error => {
        handleApiError(error);

        // Hiển thị thông báo lỗi trong bảng
        document.getElementById('inventoryTable').innerHTML = `
          <tr>
            <td colspan="9" class="text-center text-danger">
              Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại sau.
            </td>
          </tr>
        `;
      });
  }

  // Hàm hiển thị tổng quan
  function displayOverview(overview) {
    document.getElementById('totalProducts').textContent = overview.totalProducts;
    document.getElementById('totalStock').textContent = overview.totalStock;
    document.getElementById('lowStockProducts').textContent = overview.lowStockProducts;
    document.getElementById('inventoryValue').textContent = formatCurrency(overview.inventoryValue);
  }

  // Hàm hiển thị biểu đồ tồn kho theo danh mục
  function displayCategoryStockChart(stockByCategory) {
    const ctx = document.getElementById('categoryStockChart').getContext('2d');

    // Nếu biểu đồ đã tồn tại, hủy nó
    if (categoryStockChart) {
      categoryStockChart.destroy();
    }

    // Chuẩn bị dữ liệu
    const labels = stockByCategory.map(item => item.categoryName);
    const data = stockByCategory.map(item => item.stock);

    // Tạo biểu đồ mới
    categoryStockChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'Số lượng tồn kho',
          data: data,
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }

  // Hàm hiển thị biểu đồ giá trị tồn kho theo danh mục
  function displayCategoryValueChart(valueByCategory) {
    const ctx = document.getElementById('categoryValueChart').getContext('2d');

    // Nếu biểu đồ đã tồn tại, hủy nó
    if (categoryValueChart) {
      categoryValueChart.destroy();
    }

    // Chuẩn bị dữ liệu
    const labels = valueByCategory.map(item => item.categoryName);
    const data = valueByCategory.map(item => item.value);

    // Tạo biểu đồ mới
    categoryValueChart = new Chart(ctx, {
      type: 'pie',
      data: {
        labels: labels,
        datasets: [{
          data: data,
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)',
            'rgba(199, 199, 199, 0.7)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = formatCurrency(context.raw);
                return `${label}: ${value}`;
              }
            }
          }
        }
      }
    });
  }

  // Hàm hiển thị bảng chi tiết tồn kho
  function displayInventoryTable(products) {
    const tableBody = document.getElementById('inventoryTable');
    tableBody.innerHTML = '';

    if (products.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="9" class="text-center">
            Không có sản phẩm nào
          </td>
        </tr>
      `;
      return;
    }

    products.forEach((product, index) => {
      const row = document.createElement('tr');

      // Tạo badge cho trạng thái tồn kho
      let stockBadge = '';
      if (product.stock === 0) {
        stockBadge = '<span class="badge badge-out-of-stock">Hết hàng</span>';
      } else if (product.stock <= 5) {
        stockBadge = '<span class="badge badge-low-stock">Sắp hết</span>';
      } else if (product.stock >= 50) {
        stockBadge = '<span class="badge badge-overstock">Tồn kho cao</span>';
      } else {
        stockBadge = '<span class="badge badge-in-stock">Còn hàng</span>';
      }

      row.innerHTML = `
        <td>${index + 1 + currentPage * pageSize}</td>
        <td>
          <img src="${product.thumbnail || '../../assets/images/default-image.avif'}" alt="${product.name}" class="img-thumbnail">
        </td>
        <td>
          <strong>${product.name}</strong>
          <br>
          <small class="text-muted">Màu sắc: ${Array.isArray(product.colors) && product.colors.length > 0 ? product.colors.join(', ') : 'N/A'}</small>
        </td>
        <td>${product.category ? product.category.name : 'N/A'}</td>
        <td>${product.supplier ? product.supplier.name : 'N/A'}</td>
        <td>${formatCurrency(product.price)}</td>
        <td>${product.stock}</td>
        <td>${formatCurrency(product.price * product.stock)}</td>
        <td>${stockBadge}</td>
      `;

      tableBody.appendChild(row);
    });
  }

  // Hàm hiển thị phân trang
  function displayPagination(currentPage, totalPages) {
    const paginationContainer = document.getElementById('pagination');
    paginationContainer.innerHTML = '';

    if (totalPages <= 1) {
      return;
    }

    const pagination = createPagination(currentPage, totalPages, (page) => {
      currentPage = page;
      loadInventoryData();
    });

    paginationContainer.appendChild(pagination);
  }

  // Hàm thiết lập các sự kiện
  function setupEventListeners() {
    // Sự kiện nút áp dụng bộ lọc
    document.getElementById('btnApplyFilter').addEventListener('click', function() {
      currentPage = 0;
      loadInventoryData();
    });

    // Sự kiện nút xuất PDF
    document.getElementById('btnExportPDF').addEventListener('click', function() {
      exportReport('pdf');
    });

    // Sự kiện nút xuất Excel
    document.getElementById('btnExportExcel').addEventListener('click', function() {
      exportReport('excel');
    });

    // Sự kiện nút in
    document.getElementById('btnPrint').addEventListener('click', function() {
      window.print();
    });
  }

  // Hàm xuất báo cáo
  function exportReport(type) {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    const categoryId = document.getElementById('filterCategory').value;
    const supplierId = document.getElementById('filterSupplier').value;
    const stockStatus = document.getElementById('filterStock').value;

    // Xây dựng URL với các tham số lọc
    let url = `http://localhost:8080/api/reports/inventory/export?type=${type}`;

    if (categoryId) {
      url += `&categoryId=${categoryId}`;
    }

    if (supplierId) {
      url += `&supplierId=${supplierId}`;
    }

    if (stockStatus) {
      url += `&stockStatus=${stockStatus}`;
    }

    // Tải file
    window.open(url, '_blank');
  }
});
