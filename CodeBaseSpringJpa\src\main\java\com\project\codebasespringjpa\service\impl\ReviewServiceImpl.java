package com.project.codebasespringjpa.service.impl;

import com.project.codebasespringjpa.dto.review.request.ReviewCreateRequest;
import com.project.codebasespringjpa.dto.review.request.ReviewUpdateRequest;
import com.project.codebasespringjpa.dto.review.response.ReviewResponse;
import com.project.codebasespringjpa.entity.ProductEntity;
import com.project.codebasespringjpa.entity.ReviewEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.exception.AppException;
import com.project.codebasespringjpa.exception.ErrorCode;
import com.project.codebasespringjpa.mapper.ReviewMapper;
import com.project.codebasespringjpa.repository.IProductRepository;
import com.project.codebasespringjpa.repository.IReviewRepository;
import com.project.codebasespringjpa.repository.IUserRepository;
import com.project.codebasespringjpa.service.interfaces.IReviewService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ReviewServiceImpl implements IReviewService {
    IReviewRepository reviewRepository;
    IProductRepository productRepository;
    IUserRepository userRepository;
    ReviewMapper reviewMapper;

    @Override
    public List<ReviewResponse> findAll() {
        List<ReviewEntity> reviews = reviewRepository.findAll();
        return reviewMapper.toResponseList(reviews);
    }

    @Override
    public ReviewResponse findById(Long id) {
        ReviewEntity review = reviewRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.REVIEW_NOT_FOUND));
        return reviewMapper.toResponse(review);
    }

    @Override
    public List<ReviewResponse> findByProductId(Long productId) {
        ProductEntity product = productRepository.findById(productId)
                .orElseThrow(() -> new AppException(ErrorCode.PRODUCT_NOT_FOUND));
        
        List<ReviewEntity> reviews = reviewRepository.findByProduct(product);
        return reviewMapper.toResponseList(reviews);
    }

    @Override
    public List<ReviewResponse> findByUserId(Long userId) {
        UserEntity user = userRepository.findById(userId)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));
        
        List<ReviewEntity> reviews = reviewRepository.findByUser(user);
        return reviewMapper.toResponseList(reviews);
    }

    @Override
    public List<ReviewResponse> findByStatus(Boolean status) {
        List<ReviewEntity> reviews = reviewRepository.findByStatus(status);
        return reviewMapper.toResponseList(reviews);
    }

    @Override
    public Double getAverageRatingByProduct(Long productId) {
        ProductEntity product = productRepository.findById(productId)
                .orElseThrow(() -> new AppException(ErrorCode.PRODUCT_NOT_FOUND));
        
        return reviewRepository.getAverageRatingByProduct(productId);
    }

    @Override
    public ReviewResponse create(ReviewCreateRequest request) {
        ProductEntity product = productRepository.findById(request.getProductId())
                .orElseThrow(() -> new AppException(ErrorCode.PRODUCT_NOT_FOUND));
        
        UserEntity user = userRepository.findById(request.getUserId())
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));
        
        // Kiểm tra xem người dùng đã đánh giá sản phẩm này chưa
        if (reviewRepository.existsByProductAndUser(product, user)) {
            throw new AppException(ErrorCode.REVIEW_ALREADY_EXISTS);
        }
        
        ReviewEntity review = reviewMapper.toEntity(request, product, user);
        ReviewEntity savedReview = reviewRepository.save(review);
        
        return reviewMapper.toResponse(savedReview);
    }

    @Override
    public ReviewResponse update(Long id, ReviewUpdateRequest request) {
        ReviewEntity review = reviewRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.REVIEW_NOT_FOUND));
        
        reviewMapper.updateEntityFromRequest(review, request);
        ReviewEntity updatedReview = reviewRepository.save(review);
        
        return reviewMapper.toResponse(updatedReview);
    }

    @Override
    public void delete(Long id) {
        ReviewEntity review = reviewRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.REVIEW_NOT_FOUND));
        
        reviewRepository.delete(review);
    }
}
