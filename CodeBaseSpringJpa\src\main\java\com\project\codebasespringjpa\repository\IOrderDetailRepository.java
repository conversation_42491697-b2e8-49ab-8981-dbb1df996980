package com.project.codebasespringjpa.repository;

import com.project.codebasespringjpa.entity.OrderDetailEntity;
import com.project.codebasespringjpa.entity.OrderEntity;
import com.project.codebasespringjpa.entity.ProductEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IOrderDetailRepository extends JpaRepository<OrderDetailEntity, Long> {
    List<OrderDetailEntity> findByOrder(OrderEntity order);
    List<OrderDetailEntity> findByProduct(ProductEntity product);
    void deleteByOrder(OrderEntity order);

    @Query("SELECT od.product.id, SUM(od.quantity) FROM OrderDetailEntity od GROUP BY od.product.id ORDER BY SUM(od.quantity) DESC")
    List<Object[]> findTopSellingProducts();
}
