package com.project.codebasespringjpa.repository;

import com.project.codebasespringjpa.entity.SupplierEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ISupplierRepository extends JpaRepository<SupplierEntity, Long> {
//    @Query("select sp from SupplierEntity sp where sp.isDelete = false and (:keyword)")
    @Query("""
        select sp from SupplierEntity sp where sp.isDelete = false 
        and (:keyword is null or sp.name like concat('%', :keyword, '%') )       
        """)
    List<SupplierEntity> findAll(@Param("keyword") String keyword);
    Optional<SupplierEntity> findByName(String name);
    Optional<SupplierEntity> findByEmail(String email);
    boolean existsByName(String name);
    boolean existsByEmail(String email);
}
