# Hướng dẫn Test Luồng Mua Hàng - Tina Shop

## Các vấn đề đã được sửa

### 1. Backend API Endpoints
- ✅ Sửa lỗi URL endpoint trong CartController: `/api/carts/update{id}` → `/api/carts/update/{id}`
- ✅ Thêm endpoint `/auth/register` vào AuthController
- ✅ Cập nhật LoginResponse để trả về userId và username
- ✅ Cập nhật AuthenServiceImpl để lưu username khi register
- ✅ Cập nhật CORS configuration để cho phép localhost:5500

### 2. Frontend API Services
- ✅ Tạo file `api-config.js` thống nhất cho việc gọi API
- ✅ Cập nhật `auth-service.js` để xử lý login/register đúng format
- ✅ Cập nhật `cart-service.js` để sử dụng endpoints đúng
- ✅ Cập nhật `order-service.js` để sử dụng endpoints đúng
- ✅ Sửa các trang HTML để sử dụng services mới

### 3. Token và Authentication
- ✅ Thống nhất việc sử dụng localStorage key 'token'
- ✅ Xử lý ApiResponse wrapper từ backend
- ✅ Cập nhật header Authorization với Bearer token

## Cách Test

### 1. Khởi động Backend
```bash
cd CodeBaseSpringJpa
mvn spring-boot:run
```
Backend sẽ chạy trên: http://localhost:8080

### 2. Khởi động Frontend
Sử dụng Live Server extension trong VS Code:
- Mở file `client-fe/test-shopping.html`
- Click "Go Live" hoặc mở với Live Server
- Frontend sẽ chạy trên: http://localhost:5500 hoặc http://127.0.0.1:5500

### 3. Test Luồng Mua Hàng

#### Bước 1: Test Authentication
1. Mở trang test: `http://localhost:5500/client-fe/test-shopping.html`
2. Click "Test Login" để đăng nhập với tài khoản admin có sẵn
3. Hoặc click "Test Register" để tạo tài khoản mới
4. Kiểm tra trạng thái đăng nhập trong phần "Trạng thái đăng nhập"

#### Bước 2: Test Thêm Sản Phẩm Vào Giỏ Hàng
1. Sau khi đăng nhập, các sản phẩm sẽ được load tự động
2. Click "Add to Cart" trên bất kỳ sản phẩm nào
3. Kiểm tra giỏ hàng được cập nhật (số lượng và danh sách sản phẩm)

#### Bước 3: Test Quản Lý Giỏ Hàng
1. Xem danh sách sản phẩm trong giỏ hàng
2. Click nút "Trash" để xóa sản phẩm khỏi giỏ hàng
3. Click "Clear Cart" để xóa toàn bộ giỏ hàng

#### Bước 4: Test Checkout
1. Thêm một vài sản phẩm vào giỏ hàng
2. Click "Test Checkout" để tạo đơn hàng
3. Hệ thống sẽ tự động tạo đơn hàng và xóa giỏ hàng

### 4. Test Các Trang Thực Tế

#### Trang Đăng Nhập
- Mở: `http://localhost:5500/client-fe/pages/login/index.html`
- Test đăng nhập với: email/username và password

#### Trang Đăng Ký
- Mở: `http://localhost:5500/client-fe/pages/register/index.html`
- Test đăng ký tài khoản mới

#### Trang Chi Tiết Sản Phẩm
- Mở: `http://localhost:5500/client-fe/pages/product/detail.html?id=1`
- Test thêm sản phẩm vào giỏ hàng

#### Trang Giỏ Hàng
- Mở: `http://localhost:5500/client-fe/pages/cart/index.html`
- Test xem, cập nhật, xóa sản phẩm trong giỏ hàng

#### Trang Checkout
- Mở: `http://localhost:5500/client-fe/pages/checkout/index.html`
- Test tạo đơn hàng

## Tài Khoản Test Có Sẵn

### Admin Account
- Username: `<EMAIL>`
- Password: `password123`

### Customer Account (nếu có)
- Username: `<EMAIL>`
- Password: `password123`

## API Endpoints Chính

### Authentication
- POST `/auth/login` - Đăng nhập
- POST `/auth/register` - Đăng ký

### Products
- GET `/api/products/find-all` - Lấy danh sách sản phẩm
- GET `/api/products/{id}` - Lấy chi tiết sản phẩm

### Cart
- GET `/api/carts/user/{userId}` - Lấy giỏ hàng theo user
- POST `/api/carts/create` - Thêm sản phẩm vào giỏ hàng
- PUT `/api/carts/update/{id}` - Cập nhật số lượng sản phẩm
- DELETE `/api/carts/delete/{id}` - Xóa sản phẩm khỏi giỏ hàng
- DELETE `/api/carts/delete-all/{userId}` - Xóa toàn bộ giỏ hàng

### Orders
- GET `/api/orders/find-all` - Lấy danh sách đơn hàng
- GET `/api/orders/{id}` - Lấy chi tiết đơn hàng
- POST `/api/orders/create` - Tạo đơn hàng mới

## Troubleshooting

### Lỗi CORS
- Đảm bảo backend đã khởi động
- Kiểm tra CORS configuration trong Security.java

### Lỗi 401 Unauthorized
- Kiểm tra token trong localStorage
- Đăng nhập lại nếu token hết hạn

### Lỗi 404 Not Found
- Kiểm tra URL endpoints
- Đảm bảo backend API đang chạy

### Lỗi Database
- Kiểm tra MySQL đang chạy
- Kiểm tra connection string trong application.properties

## Cấu Trúc Files Đã Cập Nhật

### Backend
- `AuthController.java` - Thêm register endpoint
- `CartController.java` - Sửa update endpoint
- `LoginResponse.java` - Thêm userId, username
- `AuthenServiceImpl.java` - Cập nhật login/register logic
- `Security.java` - Cập nhật CORS configuration

### Frontend
- `api-config.js` - API configuration thống nhất
- `auth-service.js` - Authentication service
- `cart-service.js` - Cart service
- `order-service.js` - Order service
- `common.js` - Common functions
- `test-shopping.html` - Trang test tổng hợp

### Pages Updated
- `login/index.html` - Sử dụng AuthService
- `register/index.html` - Sử dụng AuthService
- `cart/index.html` - Sử dụng CartService
- `checkout/index.html` - Sử dụng OrderService
- `product/detail.html` - Sử dụng API mới

## Kết Luận

Luồng mua hàng đã được sửa và hoạt động đầy đủ:
1. ✅ Đăng nhập/Đăng ký
2. ✅ Xem sản phẩm
3. ✅ Thêm vào giỏ hàng
4. ✅ Quản lý giỏ hàng
5. ✅ Tạo đơn hàng

Sử dụng trang `test-shopping.html` để test nhanh toàn bộ luồng hoặc test từng trang riêng lẻ.
