<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> - <PERSON> sức cao cấp</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/common.css">
    <link rel="stylesheet" href="../../assets/css/layout.css">
    <style>
        /* Hero Slider */
        .hero-slider {
            position: relative;
            overflow: hidden;
            height: 500px;
        }

        .hero-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 1s ease;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
        }

        .hero-slide.active {
            opacity: 1;
        }

        .hero-content {
            max-width: 600px;
            padding: 2rem;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 5px;
            margin-left: 5rem;
        }

        .hero-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            color: var(--secondary-color);
        }

        .hero-button {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background-color: var(--primary-color);
            color: var(--white-color);
            border-radius: 3px;
            font-weight: 500;
            text-transform: uppercase;
            transition: all 0.3s ease;
        }

        .hero-button:hover {
            background-color: var(--accent-color);
            color: var(--white-color);
        }

        .slider-nav {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
        }

        .slider-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.5);
            margin: 0 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .slider-dot.active {
            background-color: var(--primary-color);
        }

        /* Categories */
        .categories {
            padding: 4rem 0;
        }

        .section-title {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
        }

        .section-title h2 {
            font-size: 2rem;
            display: inline-block;
            padding-bottom: 10px;
            position: relative;
        }

        .section-title h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 2px;
            background-color: var(--primary-color);
        }

        .category-card {
            position: relative;
            overflow: hidden;
            margin-bottom: 30px;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .category-image {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: all 0.5s ease;
        }

        .category-card:hover .category-image {
            transform: scale(1.1);
        }

        .category-content {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 1rem;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
            color: var(--white-color);
        }

        .category-title {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: var(--white-color);
        }

        .category-count {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Featured Products */
        .featured-products {
            padding: 4rem 0;
            background-color: var(--light-color);
        }

        .product-card {
            background-color: var(--white-color);
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }

        .product-card:hover {
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        .product-image {
            position: relative;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: all 0.5s ease;
        }

        .product-card:hover .product-image img {
            transform: scale(1.1);
        }

        .product-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: var(--primary-color);
            color: var(--white-color);
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .product-actions {
            position: absolute;
            bottom: -50px;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: center;
            gap: 10px;
            padding: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            transition: all 0.3s ease;
        }

        .product-card:hover .product-actions {
            bottom: 0;
        }

        .btn-action {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--white-color);
            color: var(--secondary-color);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-action:hover {
            background-color: var(--primary-color);
            color: var(--white-color);
        }

        .product-info {
            padding: 1rem;
        }

        .product-title {
            font-size: 1rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .product-title a {
            color: var(--secondary-color);
        }

        .product-title a:hover {
            color: var(--primary-color);
        }

        .product-rating {
            margin-bottom: 0.5rem;
            color: var(--warning-color);
        }

        .product-rating span {
            color: var(--gray-color);
            font-size: 0.9rem;
            margin-left: 5px;
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .current-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .old-price {
            font-size: 0.9rem;
            color: var(--gray-color);
            text-decoration: line-through;
        }

        /* Banner */
        .banner {
            padding: 4rem 0;
            background-image: url('../../assets/image/banner-bg.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }

        .banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
        }

        .banner-content {
            position: relative;
            color: var(--white-color);
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .banner-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--white-color);
        }

        .banner-text {
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        /* Latest Blogs */
        .latest-blogs {
            padding: 4rem 0;
        }

        .blog-card {
            margin-bottom: 30px;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .blog-card:hover {
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        .blog-image {
            position: relative;
            overflow: hidden;
        }

        .blog-image img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: all 0.5s ease;
        }

        .blog-card:hover .blog-image img {
            transform: scale(1.1);
        }

        .blog-date {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: var(--primary-color);
            color: var(--white-color);
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .blog-info {
            padding: 1rem;
        }

        .blog-title {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .blog-title a {
            color: var(--secondary-color);
        }

        .blog-title a:hover {
            color: var(--primary-color);
        }

        .blog-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: var(--gray-color);
        }

        .blog-meta i {
            margin-right: 5px;
            color: var(--primary-color);
        }

        .blog-excerpt {
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .blog-link {
            font-weight: 500;
            color: var(--primary-color);
        }

        .blog-link:hover {
            color: var(--accent-color);
        }

        /* Responsive */
        @media (max-width: 992px) {
            .hero-slider {
                height: 400px;
            }

            .hero-content {
                margin-left: 2rem;
                max-width: 500px;
            }

            .hero-title {
                font-size: 2rem;
            }
        }

        @media (max-width: 768px) {
            .hero-slider {
                height: 350px;
            }

            .hero-content {
                margin-left: 1rem;
                max-width: 400px;
                padding: 1.5rem;
            }

            .hero-title {
                font-size: 1.8rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .banner-title {
                font-size: 2rem;
            }
        }

        @media (max-width: 576px) {
            .hero-slider {
                height: 300px;
            }

            .hero-content {
                margin-left: 0;
                margin: 0 1rem;
                max-width: 100%;
                padding: 1rem;
            }

            .hero-title {
                font-size: 1.5rem;
            }

            .banner-title {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Include Header -->
    <div id="header-container"></div>

    <!-- Hero Slider -->
    <section class="hero-slider">
        <div class="hero-slide active" style="background-image: url('../../assets/image/slider1.jpg');">
            <div class="hero-content">
                <h1 class="hero-title">Bộ sưu tập trang sức mới</h1>
                <p class="hero-subtitle">Khám phá bộ sưu tập trang sức mới nhất với thiết kế độc đáo và tinh tế.</p>
                <a href="../product/index.html" class="hero-button">Khám phá ngay</a>
            </div>
        </div>

        <div class="hero-slide" style="background-image: url('../../assets/image/slider2.jpg');">
            <div class="hero-content">
                <h1 class="hero-title">Trang sức kim cương cao cấp</h1>
                <p class="hero-subtitle">Tỏa sáng với bộ sưu tập kim cương đẳng cấp, được chế tác tinh xảo.</p>
                <a href="../category/detail.html?slug=nhan-kim-cuong" class="hero-button">Xem bộ sưu tập</a>
            </div>
        </div>

        <div class="hero-slide" style="background-image: url('../../assets/image/slider3.jpg');">
            <div class="hero-content">
                <h1 class="hero-title">Quà tặng hoàn hảo</h1>
                <p class="hero-subtitle">Tìm kiếm món quà ý nghĩa cho người thân yêu với bộ sưu tập trang sức đa dạng.</p>
                <a href="../product/index.html?sort=newest" class="hero-button">Mua ngay</a>
            </div>
        </div>

        <div class="slider-nav">
            <div class="slider-dot active" onclick="changeSlide(0)"></div>
            <div class="slider-dot" onclick="changeSlide(1)"></div>
            <div class="slider-dot" onclick="changeSlide(2)"></div>
        </div>
    </section>

    <!-- Categories -->
    <section class="categories">
        <div class="container">
            <div class="section-title">
                <h2>Danh mục sản phẩm</h2>
            </div>

            <div class="row" id="categoriesContainer">
                <!-- Categories will be loaded here -->
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section class="featured-products">
        <div class="container">
            <div class="section-title">
                <h2>Sản phẩm nổi bật</h2>
            </div>

            <div class="row" id="featuredProductsContainer">
                <!-- Featured products will be loaded here -->
            </div>

            <div class="text-center mt-4">
                <a href="../product/index.html" class="btn btn-outline-primary">Xem tất cả sản phẩm</a>
            </div>
        </div>
    </section>

    <!-- Banner -->
    <section class="banner">
        <div class="container">
            <div class="banner-content">
                <h2 class="banner-title">Ưu đãi đặc biệt</h2>
                <p class="banner-text">Giảm giá lên đến 30% cho tất cả sản phẩm nhẫn kim cương. Chương trình khuyến mãi có hạn, hãy nhanh tay!</p>
                <a href="../category/detail.html?slug=nhan-kim-cuong" class="btn btn-primary">Mua ngay</a>
            </div>
        </div>
    </section>

    <!-- Latest Blogs -->
    <section class="latest-blogs">
        <div class="container">
            <div class="section-title">
                <h2>Tin tức mới nhất</h2>
            </div>

            <div class="row" id="latestBlogsContainer">
                <!-- Latest blogs will be loaded here -->
            </div>

            <div class="text-center mt-4">
                <a href="../blog/index.html" class="btn btn-outline-primary">Xem tất cả bài viết</a>
            </div>
        </div>
    </section>

    <!-- Include Footer -->
    <div id="footer-container"></div>

    <!-- Common JavaScript -->
    <script src="../../assets/js/common.js"></script>
    <script src="../../assets/js/services/api-config.js"></script>
    <script src="../../assets/js/services/category-service.js"></script>
    <script src="../../assets/js/services/product-service.js"></script>
    <script src="../../assets/js/services/blog-service.js"></script>

    <script>
        // Load header and footer
        document.addEventListener('DOMContentLoaded', function() {
            fetch('../../layout/header/header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header-container').innerHTML = data;
                });

            fetch('../../layout/footer/footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer-container').innerHTML = data;
                });

            // Load categories
            loadCategories();

            // Load featured products
            loadFeaturedProducts();

            // Load latest blogs
            loadLatestBlogs();

            // Start slider
            startSlider();
        });

        // Hero Slider
        let currentSlide = 0;
        const slides = document.querySelectorAll('.hero-slide');
        const dots = document.querySelectorAll('.slider-dot');

        function changeSlide(n) {
            slides[currentSlide].classList.remove('active');
            dots[currentSlide].classList.remove('active');

            currentSlide = n;

            if (currentSlide >= slides.length) {
                currentSlide = 0;
            }

            if (currentSlide < 0) {
                currentSlide = slides.length - 1;
            }

            slides[currentSlide].classList.add('active');
            dots[currentSlide].classList.add('active');
        }

        function nextSlide() {
            changeSlide(currentSlide + 1);
        }

        function startSlider() {
            setInterval(nextSlide, 5000);
        }

        // Load categories
        async function loadCategories() {
            try {
                const categoriesContainer = document.getElementById('categoriesContainer');
                categoriesContainer.innerHTML = '<div class="col-12 text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p>Đang tải danh mục...</p></div>';

                // Fetch main categories from API
                const categories = await CategoryService.getMainCategories();

                categoriesContainer.innerHTML = '';

                if (categories.length === 0) {
                    categoriesContainer.innerHTML = '<div class="col-12 text-center">Không có danh mục nào</div>';
                    return;
                }

                // For each category, get product count
                for (const category of categories.slice(0, 4)) { // Limit to 4 categories for display
                    try {
                        // Get products for this category to count them
                        const products = await ProductService.getByCategoryId(category.id);
                        const productCount = products.totalElements || 0;

                        const categoryHTML = `
                            <div class="col-lg-3 col-md-6">
                                <div class="category-card">
                                    <a href="../category/detail.html?slug=${category.slug}">
                                        <img src="${category.image || '../../assets/image/category-default.jpg'}" alt="${category.name}" class="category-image">
                                        <div class="category-content">
                                            <h3 class="category-title">${category.name}</h3>
                                            <div class="category-count">${productCount} sản phẩm</div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        `;

                        categoriesContainer.innerHTML += categoryHTML;
                    } catch (error) {
                        console.error(`Error loading products for category ${category.id}:`, error);
                    }
                }
            } catch (error) {
                console.error('Error loading categories:', error);
                const categoriesContainer = document.getElementById('categoriesContainer');
                categoriesContainer.innerHTML = '<div class="col-12 text-center text-danger">Lỗi khi tải danh mục</div>';
            }
        }

        // Load featured products
        async function loadFeaturedProducts() {
            try {
                const featuredProductsContainer = document.getElementById('featuredProductsContainer');
                featuredProductsContainer.innerHTML = '<div class="col-12 text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p>Đang tải sản phẩm nổi bật...</p></div>';

                // Fetch featured products from API
                const response = await ProductService.getByFeatured(true);
                const products = response.content || [];

                featuredProductsContainer.innerHTML = '';

                if (products.length === 0) {
                    featuredProductsContainer.innerHTML = '<div class="col-12 text-center">Không có sản phẩm nổi bật</div>';
                    return;
                }

                products.forEach(product => {
                    featuredProductsContainer.innerHTML += generateProductCard(product);
                });
            } catch (error) {
                console.error('Error loading featured products:', error);
                const featuredProductsContainer = document.getElementById('featuredProductsContainer');
                featuredProductsContainer.innerHTML = '<div class="col-12 text-center text-danger">Lỗi khi tải sản phẩm nổi bật</div>';
            }
        }

        // Load latest blogs
        async function loadLatestBlogs() {
            try {
                const latestBlogsContainer = document.getElementById('latestBlogsContainer');
                latestBlogsContainer.innerHTML = '<div class="col-12 text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p>Đang tải bài viết mới nhất...</p></div>';

                // Fetch latest blogs from API
                const response = await BlogService.getAll(0, 3); // Get first page with 3 items
                const blogs = response.content || [];

                latestBlogsContainer.innerHTML = '';

                if (blogs.length === 0) {
                    latestBlogsContainer.innerHTML = '<div class="col-12 text-center">Không có bài viết nào</div>';
                    return;
                }

                blogs.forEach(blog => {
                    const blogHTML = `
                        <div class="col-lg-4 col-md-6">
                            <div class="blog-card">
                                <div class="blog-image">
                                    <a href="../blog/detail.html?slug=${blog.slug}">
                                        <img src="${blog.thumbnail || '../../assets/image/blog-default.jpg'}" alt="${blog.title}">
                                    </a>
                                    <div class="blog-date">${formatDate(blog.createdAt)}</div>
                                </div>
                                <div class="blog-info">
                                    <h3 class="blog-title">
                                        <a href="../blog/detail.html?slug=${blog.slug}">${blog.title}</a>
                                    </h3>
                                    <div class="blog-meta">
                                        <span><i class="fas fa-user"></i> ${blog.author?.name || 'Admin'}</span>
                                        <span><i class="fas fa-eye"></i> ${blog.viewCount || 0} lượt xem</span>
                                    </div>
                                    <p class="blog-excerpt">${blog.summary || blog.content.substring(0, 100) + '...'}</p>
                                    <a href="../blog/detail.html?slug=${blog.slug}" class="blog-link">Đọc tiếp <i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div>
                        </div>
                    `;

                    latestBlogsContainer.innerHTML += blogHTML;
                });
            } catch (error) {
                console.error('Error loading latest blogs:', error);
                const latestBlogsContainer = document.getElementById('latestBlogsContainer');
                latestBlogsContainer.innerHTML = '<div class="col-12 text-center text-danger">Lỗi khi tải bài viết mới nhất</div>';
            }
        }
    </script>
</body>
</html>
