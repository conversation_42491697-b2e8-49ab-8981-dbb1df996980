package com.project.codebasespringjpa.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum OrderStatusEnum {
    PENDING,
    PROCESSING,
    SHIPPED,
    DELIVERED,
    CANCELLED;

    public static List<String> statusList() {
        List<String> statusList = Arrays.stream(OrderStatusEnum.values())
                .map(Enum::name)
                .collect(Collectors.toList());

        return statusList;
    }
}
