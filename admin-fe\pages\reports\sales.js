document.addEventListener('DOMContentLoaded', function() {
  // Kiểm tra đăng nhập
  if (!checkAuth()) {
    return;
  }

  // Load layout components
  loadLayoutComponents();

  // Khởi tạo biến toàn cục
  let revenueChart, categoryChart, paymentMethodChart;

  // Thiết lập ngày mặc định cho bộ lọc
  setupDefaultDates();

  // Load dữ liệu ban đầu
  loadReportData();

  // Xử lý sự kiện
  setupEventListeners();

  // Hàm load layout components
  function loadLayoutComponents() {
    // Load header
    fetch('../../layout/header/header.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('header-container').innerHTML = html;

        // Load header script after HTML is inserted
        const headerScript = document.createElement('script');
        headerScript.src = '../../layout/header/header.js';
        document.body.appendChild(headerScript);
      });

    // Load sidebar
    fetch('../../layout/sidebar/sidebar.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('sidebar-container').innerHTML = html;

        // Load sidebar script after HTML is inserted
        const sidebarScript = document.createElement('script');
        sidebarScript.src = '../../layout/sidebar/sidebar.js';
        document.body.appendChild(sidebarScript);
      });

    // Load footer
    fetch('../../layout/footer/footer.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('footer-container').innerHTML = html;
      });
  }

  // Hàm thiết lập ngày mặc định cho bộ lọc
  function setupDefaultDates() {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    document.getElementById('filterDateFrom').valueAsDate = firstDayOfMonth;
    document.getElementById('filterDateTo').valueAsDate = today;

    // Vô hiệu hóa các trường ngày khi không chọn tùy chỉnh
    toggleDateFields(false);
  }

  // Hàm load dữ liệu báo cáo
  function loadReportData() {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    const period = document.getElementById('filterPeriod').value;
    const dateFrom = document.getElementById('filterDateFrom').value;
    const dateTo = document.getElementById('filterDateTo').value;

    // Hiển thị loading
    document.getElementById('revenueTable').innerHTML = `
      <tr>
        <td colspan="5" class="text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Đang tải...</span>
          </div>
        </td>
      </tr>
    `;

    // Xây dựng URL với các tham số lọc
    let url = `http://localhost:8080/api/reports/sales?period=${period}`;

    if (period === 'custom' && dateFrom && dateTo) {
      url += `&dateFrom=${dateFrom}&dateTo=${dateTo}`;
    }

    axios.get(url)
      .then(response => {
        const reportData = response.data;

        // Hiển thị tổng quan
        displayOverview(reportData.overview);

        // Hiển thị biểu đồ doanh thu
        displayRevenueChart(reportData.revenueByDate);

        // Hiển thị biểu đồ danh mục
        displayCategoryChart(reportData.revenueByCategory);

        // Hiển thị biểu đồ phương thức thanh toán
        displayPaymentMethodChart(reportData.revenueByPaymentMethod);

        // Hiển thị bảng chi tiết
        displayRevenueTable(reportData.revenueByDate);
      })
      .catch(error => {
        handleApiError(error);

        // Hiển thị thông báo lỗi trong bảng
        document.getElementById('revenueTable').innerHTML = `
          <tr>
            <td colspan="5" class="text-center text-danger">
              Đã xảy ra lỗi khi tải dữ liệu. Vui lòng thử lại sau.
            </td>
          </tr>
        `;
      });
  }

  // Hàm hiển thị tổng quan
  function displayOverview(overview) {
    document.getElementById('totalRevenue').textContent = formatCurrency(overview.totalRevenue);
    document.getElementById('totalOrders').textContent = overview.totalOrders;
    document.getElementById('averageOrderValue').textContent = formatCurrency(overview.averageOrderValue);
    document.getElementById('totalProductsSold').textContent = overview.totalProductsSold;
  }

  // Hàm hiển thị biểu đồ doanh thu
  function displayRevenueChart(revenueByDate) {
    const ctx = document.getElementById('revenueChart').getContext('2d');

    // Nếu biểu đồ đã tồn tại, hủy nó
    if (revenueChart) {
      revenueChart.destroy();
    }

    // Chuẩn bị dữ liệu
    const labels = revenueByDate.map(item => item.date);
    const data = revenueByDate.map(item => item.revenue);

    // Tạo biểu đồ mới
    revenueChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'Doanh thu',
          data: data,
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 2,
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return formatCurrency(value);
              }
            }
          }
        },
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                return formatCurrency(context.raw);
              }
            }
          }
        }
      }
    });
  }

  // Hàm hiển thị biểu đồ danh mục
  function displayCategoryChart(revenueByCategory) {
    const ctx = document.getElementById('categoryChart').getContext('2d');

    // Nếu biểu đồ đã tồn tại, hủy nó
    if (categoryChart) {
      categoryChart.destroy();
    }

    // Chuẩn bị dữ liệu
    const labels = revenueByCategory.map(item => item.categoryName);
    const data = revenueByCategory.map(item => item.revenue);

    // Tạo biểu đồ mới
    categoryChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: labels,
        datasets: [{
          data: data,
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)',
            'rgba(199, 199, 199, 0.7)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = formatCurrency(context.raw);
                return `${label}: ${value}`;
              }
            }
          }
        }
      }
    });
  }

  // Hàm hiển thị biểu đồ phương thức thanh toán
  function displayPaymentMethodChart(revenueByPaymentMethod) {
    const ctx = document.getElementById('paymentMethodChart').getContext('2d');

    // Nếu biểu đồ đã tồn tại, hủy nó
    if (paymentMethodChart) {
      paymentMethodChart.destroy();
    }

    // Chuẩn bị dữ liệu
    const labels = revenueByPaymentMethod.map(item => item.paymentMethod);
    const data = revenueByPaymentMethod.map(item => item.revenue);

    // Tạo biểu đồ mới
    paymentMethodChart = new Chart(ctx, {
      type: 'pie',
      data: {
        labels: labels,
        datasets: [{
          data: data,
          backgroundColor: [
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 99, 132, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = formatCurrency(context.raw);
                return `${label}: ${value}`;
              }
            }
          }
        }
      }
    });
  }

  // Hàm hiển thị bảng chi tiết
  function displayRevenueTable(revenueByDate) {
    const tableBody = document.getElementById('revenueTable');
    tableBody.innerHTML = '';

    if (revenueByDate.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="5" class="text-center">
            Không có dữ liệu
          </td>
        </tr>
      `;
      return;
    }

    revenueByDate.forEach(item => {
      const row = document.createElement('tr');

      row.innerHTML = `
        <td>${item.date}</td>
        <td>${item.orderCount}</td>
        <td>${formatCurrency(item.revenue)}</td>
        <td>${item.productsSold}</td>
        <td>${formatCurrency(item.averageOrderValue)}</td>
      `;

      tableBody.appendChild(row);
    });
  }

  // Hàm thiết lập các sự kiện
  function setupEventListeners() {
    // Sự kiện thay đổi khoảng thời gian
    document.getElementById('filterPeriod').addEventListener('change', function() {
      const isCustom = this.value === 'custom';
      toggleDateFields(isCustom);
    });

    // Sự kiện nút áp dụng bộ lọc
    document.getElementById('btnApplyFilter').addEventListener('click', function() {
      loadReportData();
    });

    // Sự kiện nút xuất PDF
    document.getElementById('btnExportPDF').addEventListener('click', function() {
      exportReport('pdf');
    });

    // Sự kiện nút xuất Excel
    document.getElementById('btnExportExcel').addEventListener('click', function() {
      exportReport('excel');
    });

    // Sự kiện nút in
    document.getElementById('btnPrint').addEventListener('click', function() {
      window.print();
    });
  }

  // Hàm bật/tắt các trường ngày
  function toggleDateFields(enabled) {
    document.getElementById('filterDateFrom').disabled = !enabled;
    document.getElementById('filterDateTo').disabled = !enabled;
  }

  // Hàm xuất báo cáo
  function exportReport(type) {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    const period = document.getElementById('filterPeriod').value;
    const dateFrom = document.getElementById('filterDateFrom').value;
    const dateTo = document.getElementById('filterDateTo').value;

    // Xây dựng URL với các tham số lọc
    let url = `http://localhost:8080/api/reports/sales/export?period=${period}&type=${type}`;

    if (period === 'custom' && dateFrom && dateTo) {
      url += `&dateFrom=${dateFrom}&dateTo=${dateTo}`;
    }

    // Tải file
    window.open(url, '_blank');
  }
});
