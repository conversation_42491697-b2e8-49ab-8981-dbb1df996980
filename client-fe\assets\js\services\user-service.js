// User Service for Client

// User API
const UserService = {
    getProfile: async () => {
        return await apiRequest('users/profile');
    },
    
    updateProfile: async (userData) => {
        return await apiRequest('users/profile', 'PUT', userData);
    },
    
    changePassword: async (passwordData) => {
        return await apiRequest('users/change-password', 'PUT', passwordData);
    }
};
