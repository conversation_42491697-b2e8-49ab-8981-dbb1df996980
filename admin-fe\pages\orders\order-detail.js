document.addEventListener('DOMContentLoaded', function() {
  // Ki<PERSON>m tra đăng nhập
  if (!checkAuth()) {
    return;
  }
  
  // Load layout components
  loadLayoutComponents();
  
  // Khởi tạo biến toàn cục
  let updateStatusModal;
  let orderId;
  let orderData;
  
  // Lấy ID đơn hàng từ URL
  const urlParams = new URLSearchParams(window.location.search);
  orderId = urlParams.get('id');
  
  if (!orderId) {
    showErrorToast('Không tìm thấy ID đơn hàng');
    setTimeout(() => {
      window.location.href = 'orders.html';
    }, 2000);
    return;
  }
  
  // Khởi tạo modal
  updateStatusModal = new bootstrap.Modal(document.getElementById('updateStatusModal'));
  
  // Load dữ liệu đơn hàng
  loadOrderDetail(orderId);
  
  // Xử lý sự kiện
  setupEventListeners();
  
  // Hàm load layout components
  function loadLayoutComponents() {
    // Load header
    fetch('/layout/header/header.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('header-container').innerHTML = html;
        
        // Load header script after HTML is inserted
        const headerScript = document.createElement('script');
        headerScript.src = '/layout/header/header.js';
        document.body.appendChild(headerScript);
      });
    
    // Load sidebar
    fetch('/layout/sidebar/sidebar.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('sidebar-container').innerHTML = html;
        
        // Load sidebar script after HTML is inserted
        const sidebarScript = document.createElement('script');
        sidebarScript.src = '/layout/sidebar/sidebar.js';
        document.body.appendChild(sidebarScript);
      });
    
    // Load footer
    fetch('/layout/footer/footer.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('footer-container').innerHTML = html;
      });
  }
  
  // Hàm load chi tiết đơn hàng
  function loadOrderDetail(id) {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    axios.get(`http://localhost:8080/api/orders/${id}`)
      .then(response => {
        orderData = response.data;
        
        // Hiển thị thông tin đơn hàng
        displayOrderInfo(orderData);
        
        // Hiển thị thông tin khách hàng
        displayCustomerInfo(orderData);
        
        // Hiển thị chi tiết đơn hàng
        displayOrderItems(orderData);
        
        // Hiển thị lịch sử đơn hàng (giả định)
        displayOrderHistory(orderData);
      })
      .catch(error => {
        handleApiError(error);
        showErrorToast('Không thể tải thông tin đơn hàng');
      });
  }
  
  // Hàm hiển thị thông tin đơn hàng
  function displayOrderInfo(order) {
    document.getElementById('orderIdTitle').textContent = `#${order.id}`;
    document.getElementById('orderId').textContent = `#${order.id}`;
    document.getElementById('orderDate').textContent = formatDate(order.orderDate);
    
    // Hiển thị trạng thái
    let statusBadge = '';
    let statusText = '';
    
    switch (order.status) {
      case 'PENDING':
        statusBadge = 'badge-pending';
        statusText = 'Chờ xử lý';
        break;
      case 'PROCESSING':
        statusBadge = 'badge-processing';
        statusText = 'Đang xử lý';
        break;
      case 'SHIPPED':
        statusBadge = 'badge-shipped';
        statusText = 'Đang giao';
        break;
      case 'DELIVERED':
        statusBadge = 'badge-delivered';
        statusText = 'Đã giao';
        break;
      case 'CANCELLED':
        statusBadge = 'badge-cancelled';
        statusText = 'Đã hủy';
        break;
      default:
        statusBadge = 'bg-secondary';
        statusText = order.status || 'Không xác định';
    }
    
    document.getElementById('orderStatus').innerHTML = `<span class="badge ${statusBadge}">${statusText}</span>`;
    document.getElementById('paymentMethod').textContent = order.paymentMethod || 'Không có thông tin';
    document.getElementById('paymentStatus').textContent = order.paymentStatus || 'Không có thông tin';
    document.getElementById('orderNote').textContent = order.note || 'Không có ghi chú';
  }
  
  // Hàm hiển thị thông tin khách hàng
  function displayCustomerInfo(order) {
    if (order.user) {
      document.getElementById('customerName').textContent = order.user.fullname || 'N/A';
      document.getElementById('customerEmail').textContent = order.user.email || 'N/A';
      document.getElementById('customerPhone').textContent = order.user.phone || 'N/A';
    } else {
      document.getElementById('customerName').textContent = 'N/A';
      document.getElementById('customerEmail').textContent = 'N/A';
      document.getElementById('customerPhone').textContent = 'N/A';
    }
    
    document.getElementById('shippingAddress').textContent = order.shippingAddress || 'Không có thông tin';
  }
  
  // Hàm hiển thị chi tiết đơn hàng
  function displayOrderItems(order) {
    const tableBody = document.getElementById('orderItems');
    tableBody.innerHTML = '';
    
    if (!order.orderDetails || order.orderDetails.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="5" class="text-center">Không có sản phẩm nào</td>
        </tr>
      `;
      return;
    }
    
    let subtotal = 0;
    
    order.orderDetails.forEach(item => {
      const row = document.createElement('tr');
      
      const productName = item.product ? item.product.name : 'N/A';
      const variantName = item.variant ? item.variant.name : 'Không có';
      const price = item.price || 0;
      const quantity = item.quantity || 0;
      const itemTotal = price * quantity;
      
      subtotal += itemTotal;
      
      row.innerHTML = `
        <td>
          <div class="d-flex align-items-center">
            <img src="${item.product && item.product.thumbnail ? item.product.thumbnail : '../../assets/image/default-image.avif'}" 
                 alt="${productName}" class="img-thumbnail me-2" style="width: 50px; height: 50px; object-fit: cover;">
            <span>${productName}</span>
          </div>
        </td>
        <td>${variantName}</td>
        <td>${formatCurrency(price)}</td>
        <td>${quantity}</td>
        <td class="text-end">${formatCurrency(itemTotal)}</td>
      `;
      
      tableBody.appendChild(row);
    });
    
    // Hiển thị tổng tiền
    const discount = order.discountAmount || 0;
    const total = order.totalAmount || subtotal - discount;
    
    document.getElementById('subtotal').textContent = formatCurrency(subtotal);
    document.getElementById('discount').textContent = formatCurrency(discount);
    document.getElementById('total').textContent = formatCurrency(total);
  }
  
  // Hàm hiển thị lịch sử đơn hàng
  function displayOrderHistory(order) {
    const historyContainer = document.getElementById('orderHistory');
    historyContainer.innerHTML = '';
    
    // Tạo lịch sử giả định dựa trên trạng thái hiện tại
    const histories = [];
    
    // Đơn hàng luôn có trạng thái "Đã tạo đơn"
    histories.push({
      status: 'Đã tạo đơn',
      date: order.orderDate,
      note: 'Đơn hàng đã được tạo thành công'
    });
    
    // Thêm các trạng thái khác tùy thuộc vào trạng thái hiện tại
    switch (order.status) {
      case 'CANCELLED':
        histories.push({
          status: 'Đã hủy',
          date: new Date(new Date(order.orderDate).getTime() + 1000 * 60 * 60), // Giả định 1 giờ sau
          note: 'Đơn hàng đã bị hủy'
        });
        break;
      case 'PROCESSING':
        histories.push({
          status: 'Đang xử lý',
          date: new Date(new Date(order.orderDate).getTime() + 1000 * 60 * 60), // Giả định 1 giờ sau
          note: 'Đơn hàng đang được xử lý'
        });
        break;
      case 'SHIPPED':
        histories.push({
          status: 'Đang xử lý',
          date: new Date(new Date(order.orderDate).getTime() + 1000 * 60 * 60), // Giả định 1 giờ sau
          note: 'Đơn hàng đang được xử lý'
        });
        histories.push({
          status: 'Đang giao',
          date: new Date(new Date(order.orderDate).getTime() + 1000 * 60 * 60 * 24), // Giả định 1 ngày sau
          note: 'Đơn hàng đang được giao đến bạn'
        });
        break;
      case 'DELIVERED':
        histories.push({
          status: 'Đang xử lý',
          date: new Date(new Date(order.orderDate).getTime() + 1000 * 60 * 60), // Giả định 1 giờ sau
          note: 'Đơn hàng đang được xử lý'
        });
        histories.push({
          status: 'Đang giao',
          date: new Date(new Date(order.orderDate).getTime() + 1000 * 60 * 60 * 24), // Giả định 1 ngày sau
          note: 'Đơn hàng đang được giao đến bạn'
        });
        histories.push({
          status: 'Đã giao',
          date: new Date(new Date(order.orderDate).getTime() + 1000 * 60 * 60 * 24 * 3), // Giả định 3 ngày sau
          note: 'Đơn hàng đã được giao thành công'
        });
        break;
    }
    
    // Hiển thị lịch sử
    histories.forEach(history => {
      const item = document.createElement('li');
      item.className = 'timeline-item';
      
      item.innerHTML = `
        <div class="timeline-badge"><i class="bi bi-circle-fill"></i></div>
        <div class="timeline-panel">
          <div class="timeline-heading">
            <h6 class="timeline-title">${history.status}</h6>
            <p><small class="text-muted"><i class="bi bi-clock"></i> ${formatDate(history.date)}</small></p>
          </div>
          <div class="timeline-body">
            <p>${history.note}</p>
          </div>
        </div>
      `;
      
      historyContainer.appendChild(item);
    });
  }
  
  // Hàm cập nhật trạng thái đơn hàng
  function updateOrderStatus() {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    const status = document.getElementById('orderStatusSelect').value;
    const note = document.getElementById('statusNote').value;
    
    const updateData = {
      status: status,
      note: note
    };
    
    axios.put(`http://localhost:8080/api/orders/${orderId}/status`, updateData)
      .then(response => {
        showSuccessToast('Cập nhật trạng thái đơn hàng thành công');
        updateStatusModal.hide();
        loadOrderDetail(orderId);
      })
      .catch(error => {
        handleApiError(error);
      });
  }
  
  // Hàm thiết lập các sự kiện
  function setupEventListeners() {
    // Sự kiện nút cập nhật trạng thái
    document.getElementById('btnUpdateStatus').addEventListener('click', function() {
      document.getElementById('orderStatusSelect').value = orderData.status;
      document.getElementById('statusNote').value = '';
      updateStatusModal.show();
    });
    
    // Sự kiện nút lưu trạng thái
    document.getElementById('btnSaveStatus').addEventListener('click', updateOrderStatus);
    
    // Sự kiện nút in đơn hàng
    document.getElementById('btnPrint').addEventListener('click', function() {
      window.print();
    });
  }
});
