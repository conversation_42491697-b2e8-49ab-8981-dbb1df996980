// Wishlist Service for Client

// Wishlist API
const WishlistService = {
    getByUserId: async (userId) => {
        return await apiRequest(`wishlists/user/${userId}`);
    },
    
    add: async (wishlistData) => {
        return await apiRequest('wishlists', 'POST', wishlistData);
    },
    
    remove: async (id) => {
        return await apiRequest(`wishlists/${id}`, 'DELETE');
    },
    
    check: async (userId, productId) => {
        return await apiRequest(`wishlists/check?userId=${userId}&productId=${productId}`);
    },
    
    // Local storage wishlist functions for non-authenticated users
    getLocalWishlist: () => {
        return JSON.parse(localStorage.getItem('wishlist')) || [];
    },
    
    saveLocalWishlist: (wishlist) => {
        localStorage.setItem('wishlist', JSON.stringify(wishlist));
    },
    
    addLocalItem: (productId) => {
        const wishlist = WishlistService.getLocalWishlist();
        
        // Check if product already exists in wishlist
        if (!wishlist.includes(productId)) {
            wishlist.push(productId);
            WishlistService.saveLocalWishlist(wishlist);
        }
        
        return wishlist;
    },
    
    removeLocalItem: (productId) => {
        let wishlist = WishlistService.getLocalWishlist();
        
        // Remove the item
        wishlist = wishlist.filter(id => id !== productId);
        
        WishlistService.saveLocalWishlist(wishlist);
        return wishlist;
    },
    
    checkLocalItem: (productId) => {
        const wishlist = WishlistService.getLocalWishlist();
        return wishlist.includes(productId);
    },
    
    clearLocalWishlist: () => {
        localStorage.removeItem('wishlist');
        return [];
    },
    
    getLocalWishlistCount: () => {
        const wishlist = WishlistService.getLocalWishlist();
        return wishlist.length;
    },
    
    // Sync local wishlist with server when user logs in
    syncWishlistWithServer: async (userId) => {
        const localWishlist = WishlistService.getLocalWishlist();
        
        if (localWishlist.length === 0) {
            return;
        }
        
        try {
            // Add each local wishlist item to server
            for (const productId of localWishlist) {
                await WishlistService.add({
                    userId: userId,
                    productId: productId
                });
            }
            
            // Clear local wishlist after syncing
            WishlistService.clearLocalWishlist();
        } catch (error) {
            console.error('Error syncing wishlist with server:', error);
            throw error;
        }
    }
};
