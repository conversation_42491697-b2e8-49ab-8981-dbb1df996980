package com.project.codebasespringjpa.dto.report;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SalesReportResponse {
    SalesOverview overview;
    List<SalesByDate> revenueByDate;
    List<SalesByCategory> revenueByCategory;
    List<SalesByPaymentMethod> revenueByPaymentMethod;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class SalesOverview {
        BigDecimal totalRevenue;
        int totalOrders;
        BigDecimal averageOrderValue;
        int totalProductsSold;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class SalesByDate {
        String date;
        int orderCount;
        BigDecimal revenue;
        int productsSold;
        BigDecimal averageOrderValue;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class SalesByCategory {
        String categoryName;
        BigDecimal revenue;
        int orderCount;
        int productsSold;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class SalesByPaymentMethod {
        String paymentMethod;
        BigDecimal revenue;
        int orderCount;
    }
}
