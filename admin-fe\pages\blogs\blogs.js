document.addEventListener('DOMContentLoaded', function() {
  // Ki<PERSON>m tra đăng nhập
  if (!checkAuth()) {
    return;
  }
  
  // Khởi tạo biến toàn cục
  let currentPage = 0;
  let pageSize = 10;
  let totalPages = 0;
  let blogModal;
  let selectedImage = null;
  let editorInstance = null; // Biến lưu instance của CKEditor
  
  // Load layout components
  loadLayoutComponents();
  
  // Load dữ liệu ban đầu
  loadBlogs(currentPage, pageSize);
  
  // Khởi tạo modal sau khi DOM đã load
  setTimeout(() => {
    const modalElement = document.getElementById('blogModal');
    if (modalElement) {
      blogModal = new bootstrap.Modal(modalElement);
      setupEventListeners();
    }
  }, 500);
  
  // Hàm load layout components
  function loadLayoutComponents() {
    // Load header
    fetch('../../layout/header/header.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('header-container').innerHTML = html;
        
        // Load header script after HTML is inserted
        const headerScript = document.createElement('script');
        headerScript.src = '../../layout/header/header.js';
        document.body.appendChild(headerScript);
      });
    
    // Load sidebar
    fetch('../../layout/sidebar/sidebar.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('sidebar-container').innerHTML = html;
        
        // Load sidebar script after HTML is inserted
        const sidebarScript = document.createElement('script');
        sidebarScript.src = '../../layout/sidebar/sidebar.js';
        document.body.appendChild(sidebarScript);
      });
    
    // Load footer
    fetch('../../layout/footer/footer.html')
      .then(response => response.text())
      .then(html => {
        document.getElementById('footer-container').innerHTML = html;
      });
  }
  
  // Hàm load bài viết
  function loadBlogs(page, size, keyword = '') {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    document.getElementById('blogTable').innerHTML = `<tr><td colspan="5" class="text-center">Đang tải...</td></tr>`;
    let url = `http://localhost:8080/api/blogs/find-all?page=${page+1}&limit=${size}`;
    if (keyword) url += `&keyword=${encodeURIComponent(keyword)}`;
    axios.get(url)
      .then(response => {
        let blogs = response.data.content || [];
        let totalElements = response.data.totalElements || blogs.length;
        totalPages = response.data.totalPages || 1;
        document.getElementById('totalBlogs').textContent = totalElements;
        displayBlogs(blogs);
        displayPagination(page, totalPages);
      })
      .catch(error => {
        handleApiError(error);
        document.getElementById('blogTable').innerHTML = `<tr><td colspan="5" class="text-center text-danger">Đã xảy ra lỗi khi tải dữ liệu.</td></tr>`;
      });
  }
  
  // Hàm hiển thị danh sách bài viết
  function displayBlogs(blogs) {
    const tableBody = document.getElementById('blogTable');
    tableBody.innerHTML = '';
    if (!blogs || blogs.length === 0) {
      tableBody.innerHTML = `<tr><td colspan="5" class="text-center">Không có bài viết nào</td></tr>`;
      return;
    }
    blogs.forEach((blog, index) => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${index + 1 + currentPage * pageSize}</td>
        <td><img src="${blog.image ? 'http://localhost:8080/' + blog.image : '../../assets/image/default-image.avif'}" class="img-thumbnail" style="max-width:60px;max-height:60px;"></td>
        <td>${blog.title}</td>
        <td>${blog.createDate ? formatDate(blog.createDate) : ''}</td>
        <td>
          <button class="btn btn-sm btn-info me-1 btn-edit" data-id="${blog.id}"><i class="bi bi-pencil"></i></button>
          <button class="btn btn-sm btn-danger btn-delete" data-id="${blog.id}"><i class="bi bi-trash"></i></button>
        </td>
      `;
      tableBody.appendChild(row);
    });
    document.querySelectorAll('.btn-edit').forEach(btn => {
      btn.addEventListener('click', function() {
        editBlog(this.getAttribute('data-id'));
      });
    });
    document.querySelectorAll('.btn-delete').forEach(btn => {
      btn.addEventListener('click', function() {
        deleteBlog(this.getAttribute('data-id'));
      });
    });
  }
  
  // Hàm hiển thị phân trang
  function displayPagination(currentPage, totalPages) {
    const paginationContainer = document.getElementById('pagination');
    paginationContainer.innerHTML = '';
    if (totalPages <= 1) return;
    const pagination = createPagination(currentPage, totalPages, (page) => {
      currentPage = page;
      loadBlogs(currentPage, pageSize);
    });
    paginationContainer.appendChild(pagination);
  }
  
  // Hàm thiết lập các sự kiện
  function setupEventListeners() {
    // Nút thêm bài viết
    const btnAddBlog = document.getElementById('btnAddBlog');
    if (btnAddBlog) {
      btnAddBlog.addEventListener('click', () => {
        resetBlogForm();
        const modalLabel = document.getElementById('blogModalLabel');
        if (modalLabel) modalLabel.textContent = 'Thêm bài viết';
        if (blogModal) blogModal.show();
      });
    }
    // Nút tìm kiếm
    const btnSearch = document.getElementById('btnSearch');
    if (btnSearch) {
      btnSearch.addEventListener('click', () => {
        const keyword = document.getElementById('searchBlog').value;
        currentPage = 0;
        loadBlogs(currentPage, pageSize, keyword);
      });
    }
    // Upload ảnh preview
    const blogImage = document.getElementById('blogImage');
    if (blogImage) {
      blogImage.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            const preview = document.getElementById('blogImagePreview');
            if (preview) preview.innerHTML = `<img src="${e.target.result}" class="img-thumbnail" style="max-height: 120px;">`;
          };
          reader.readAsDataURL(file);
        }
      });
    }
    // Nút lưu bài viết
    const btnSaveBlog = document.getElementById('btnSaveBlog');
    if (btnSaveBlog) {
      btnSaveBlog.addEventListener('click', saveBlog);
    }
    // Sự kiện modal hiển thị
    const blogModalElement = document.getElementById('blogModal');
    if (blogModalElement) {
      blogModalElement.addEventListener('shown.bs.modal', () => {
        // Khởi tạo CKEditor khi modal hiển thị
        if (!editorInstance) {
          ClassicEditor
            .create(document.querySelector('#blogContent'))
            .then(editor => {
              editorInstance = editor;
            })
            .catch(error => {
              console.error(error);
            });
        } else {
            // Nếu đã có instance, chỉ cần focus (hoặc reset nội dung nếu cần)
            // editorInstance.setData(''); // Reset nội dung nếu cần
        }
      });

      // Sự kiện modal ẩn
      blogModalElement.addEventListener('hidden.bs.modal', () => {
        // Dọn dẹp CKEditor khi modal ẩn
        if (editorInstance) {
          // editorInstance.destroy()
          //   .catch(error => {
          //     console.error(error);
          //   });
          // editorInstance = null;
          // Thay vì destroy, ta chỉ clear nội dung
           editorInstance.setData('');
        }
      });
    }
  }
  
  function resetBlogForm() {
    const form = document.getElementById('blogForm');
    if (form) form.reset();
    const preview = document.getElementById('blogImagePreview');
    if (preview) preview.innerHTML = '';
    document.getElementById('blogId').value = '';
    selectedImage = null;
    // Clear CKEditor content
    if (editorInstance) {
        editorInstance.setData('');
    }
  }
  
  async function saveBlog() {
    const blogId = document.getElementById('blogId').value;
    const title = document.getElementById('blogTitle').value;
    // Lấy nội dung từ CKEditor
    const content = editorInstance ? editorInstance.getData() : document.getElementById('blogContent').value;;
    let image = null;
    const blogImage = document.getElementById('blogImage');
    if (blogImage && blogImage.files && blogImage.files[0]) {
      const formData = new FormData();
      formData.append('file', blogImage.files[0]);
      try {
        const uploadRes = await axios.post('http://localhost:8080/files/upload', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
        image = uploadRes.data;
      } catch (err) {
        Swal.fire({ icon: 'error', title: 'Lỗi', text: 'Upload ảnh thất bại!' });
        return;
      }
    } else if (selectedImage) {
      image = selectedImage;
    }
    const blogData = { title, content, image };
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    const isEdit = !!blogId;
    const url = isEdit
      ? `http://localhost:8080/api/blogs/update/${blogId}`
      : 'http://localhost:8080/api/blogs/create';
    const method = isEdit ? 'put' : 'post';
    axios[method](url, blogData)
      .then(response => {
        Swal.fire({ icon: 'success', title: 'Thành công', text: isEdit ? 'Cập nhật bài viết thành công' : 'Thêm bài viết thành công' }).then(() => {
          if (blogModal) blogModal.hide();
          loadBlogs(currentPage, pageSize);
        });
      })
      .catch(error => {
        handleApiError(error);
      });
  }
  
  function editBlog(blogId) {
    const token = localStorage.getItem('token');
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    axios.get(`http://localhost:8080/api/blogs/${blogId}`)
      .then(response => {
        const blog = response.data;
        document.getElementById('blogId').value = blog.id;
        document.getElementById('blogTitle').value = blog.title;
        // Đổ nội dung vào CKEditor
        if (editorInstance) {
            editorInstance.setData(blog.content || '');
        } else {
             document.getElementById('blogContent').value = blog.content || '';
        }
        selectedImage = blog.image || null;
        const preview = document.getElementById('blogImagePreview');
        if (preview && blog.image) {
          preview.innerHTML = `<img src="http://localhost:8080/${blog.image}" class="img-thumbnail" style="max-height: 120px;">`;
        } else if (preview) {
          preview.innerHTML = '';
        }
        const modalLabel = document.getElementById('blogModalLabel');
        if (modalLabel) modalLabel.textContent = 'Cập nhật bài viết';
        if (blogModal) blogModal.show();
      })
      .catch(error => {
        handleApiError(error);
      });
  }
  
  function deleteBlog(blogId) {
    Swal.fire({
      title: 'Xác nhận xóa',
      text: 'Bạn có chắc chắn muốn xóa bài viết này?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy'
    }).then((result) => {
      if (result.isConfirmed) {
        const token = localStorage.getItem('token');
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        axios.delete(`http://localhost:8080/api/blogs/delete/${blogId}`)
          .then(() => {
            Swal.fire({ icon: 'success', title: 'Đã xóa', text: 'Xóa bài viết thành công' });
            loadBlogs(currentPage, pageSize);
          })
          .catch(error => {
            handleApiError(error);
          });
      }
    });
  }
  
  function formatDate(dateString) {
    if (!dateString) return '';
    const d = new Date(dateString);
    return d.toLocaleString('vi-VN');
  }
});
