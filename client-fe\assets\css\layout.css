/* Layout CSS for Client */

/* Header */
.header {
    background-color: var(--white-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-top {
    background-color: var(--secondary-color);
    color: var(--white-color);
    padding: 8px 0;
    font-size: 0.9rem;
}

.header-top a {
    color: var(--white-color);
}

.header-top a:hover {
    color: var(--primary-color);
}

.header-top-contact {
    display: flex;
    align-items: center;
}

.header-top-contact i {
    margin-right: 5px;
}

.header-top-contact span {
    margin-right: 15px;
}

.header-top-links {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.header-top-links a {
    margin-left: 15px;
}

.header-main {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    max-height: 60px;
}

.logo-text {
    font-family: var(--heading-font);
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-left: 10px;
}

.search-form {
    display: flex;
    width: 100%;
    max-width: 400px;
    margin: 0 20px;
}

.search-form input {
    flex: 1;
    border: 1px solid #ddd;
    border-right: none;
    padding: 10px 15px;
    border-radius: 4px 0 0 4px;
    outline: none;
}

.search-form button {
    background-color: var(--primary-color);
    color: var(--white-color);
    border: none;
    padding: 10px 15px;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

.header-actions {
    display: flex;
    align-items: center;
}

.header-action-item {
    margin-left: 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.header-action-icon {
    font-size: 1.5rem;
    color: var(--secondary-color);
}

.header-action-text {
    font-size: 0.8rem;
    margin-top: 5px;
}

.header-action-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary-color);
    color: var(--white-color);
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Navigation */
.nav {
    background-color: var(--primary-color);
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 15px 20px;
    color: var(--white-color);
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-item.active .nav-link {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    display: none;
    z-index: 1000;
}

.nav-item:hover .nav-dropdown {
    display: block;
}

.nav-dropdown-item {
    display: block;
    padding: 10px 20px;
    color: var(--secondary-color);
    border-bottom: 1px solid #f1f1f1;
}

.nav-dropdown-item:hover {
    background-color: #f9f9f9;
    color: var(--primary-color);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--white-color);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 15px;
}

/* Footer */
.footer {
    background-color: var(--secondary-color);
    color: var(--light-color);
    padding: 60px 0 0;
}

.footer-title {
    color: var(--white-color);
    font-size: 1.2rem;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-about p {
    margin-bottom: 20px;
    line-height: 1.8;
}

.footer-contact-item {
    display: flex;
    margin-bottom: 15px;
}

.footer-contact-icon {
    margin-right: 15px;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-link {
    margin-bottom: 10px;
}

.footer-link a {
    color: var(--light-color);
    transition: all 0.3s ease;
}

.footer-link a:hover {
    color: var(--primary-color);
    padding-left: 5px;
}

.footer-newsletter p {
    margin-bottom: 20px;
}

.newsletter-form {
    display: flex;
}

.newsletter-form input {
    flex: 1;
    padding: 10px 15px;
    border: none;
    border-radius: 4px 0 0 4px;
    outline: none;
}

.newsletter-form button {
    background-color: var(--primary-color);
    color: var(--white-color);
    border: none;
    padding: 10px 15px;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

.footer-bottom {
    background-color: #1a1a1a;
    padding: 20px 0;
    margin-top: 40px;
    text-align: center;
}

.footer-bottom p {
    margin: 0;
}

.social-links {
    display: flex;
    justify-content: center;
    margin-top: 15px;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin: 0 5px;
    color: var(--white-color);
    transition: all 0.3s ease;
}

.social-link:hover {
    background-color: var(--primary-color);
    color: var(--white-color);
}

/* Responsive */
@media (max-width: 992px) {
    .header-main {
        flex-wrap: wrap;
    }
    
    .logo {
        margin-bottom: 15px;
    }
    
    .search-form {
        order: 3;
        max-width: 100%;
        margin: 15px 0 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .nav-list {
        flex-direction: column;
        display: none;
    }
    
    .nav-list.show {
        display: flex;
    }
    
    .nav-dropdown {
        position: static;
        box-shadow: none;
        display: none;
        background-color: rgba(0, 0, 0, 0.1);
    }
    
    .nav-item.active .nav-dropdown {
        display: block;
    }
    
    .nav-dropdown-item {
        color: var(--white-color);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .nav-dropdown-item:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

@media (max-width: 768px) {
    .header-top {
        display: none;
    }
    
    .header-action-text {
        display: none;
    }
}
