package com.project.codebasespringjpa.dto.wishlist.response;

import com.project.codebasespringjpa.dto.product.response.ProductResponse;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WishlistResponse {
    Long id;
    Long userId;
    String userName;
    String userEmail;
    ProductResponse product;
    LocalDateTime addedDate;
    LocalDateTime createDate;
    String createBy;
    LocalDateTime updateDate;
    String updateBy;
}
