package com.project.codebasespringjpa.dto.review.response;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ReviewResponse {
    Long id;
    Long productId;
    String productName;
    String productThumbnail;
    Long userId;
    String userName;
    String userEmail;
    Integer rating;
    String comment;
    LocalDateTime reviewDate;
    Boolean status;
    LocalDateTime createDate;
    String createBy;
    LocalDateTime updateDate;
    String updateBy;
}
