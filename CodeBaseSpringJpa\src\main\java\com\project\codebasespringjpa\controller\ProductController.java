package com.project.codebasespringjpa.controller;

import com.project.codebasespringjpa.dto.product.request.ProductRequest;
import com.project.codebasespringjpa.dto.product.request.ProductSearch;
import com.project.codebasespringjpa.dto.product.response.ProductResponse;
import com.project.codebasespringjpa.service.interfaces.IProductService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/products")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ProductController {
    IProductService productService;

    @GetMapping("find-all")
    public ResponseEntity<Page<ProductResponse>> findAll(@RequestParam(name = "keyword", required = false) String keyword,
                                                         @RequestParam(name = "categoryName", required = false) String categoryName,
                                                         @RequestParam(name = "suplierName", required = false) String suplierName,
                                                         @RequestParam(name = "categoryId", required = false) Long categoryId,
                                                         @RequestParam(name = "supplierId", required = false) Long supplierId,
                                                         @RequestParam(name = "page", defaultValue = "1") Integer page,
                                                         @RequestParam(name = "limit", defaultValue = "5") Integer limit) {

        Pageable pageable = PageRequest.of(page-1, limit);

        ProductSearch productSearch = ProductSearch.builder()
                .keyword(keyword)
                .categoryName(categoryName)
                .supplierName(suplierName)
                .categoryId(categoryId)
                .supplierId(supplierId)
                .build();

        return ResponseEntity.ok(productService.findAll(productSearch, pageable));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ProductResponse> findById(@PathVariable Long id) {
        return ResponseEntity.ok(productService.findById(id));
    }

    @PostMapping("/create")
    public ResponseEntity<ProductResponse> create(@RequestBody ProductRequest request) {
        return new ResponseEntity<>(productService.create(request), HttpStatus.CREATED);
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<ProductResponse> update(@PathVariable Long id, @RequestBody ProductRequest request) {
        return ResponseEntity.ok(productService.update(id, request));
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        productService.delete(id);
        return ResponseEntity.noContent().build();
    }
}
