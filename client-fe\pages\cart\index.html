<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Giỏ hàng - Tina Shop</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.min.css">
    <style>
        .cart-item {
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }
        
        .cart-item-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .cart-item-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .cart-item-price {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .cart-item-original-price {
            text-decoration: line-through;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .quantity-control {
            display: flex;
            align-items: center;
        }
        
        .quantity-btn {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .quantity-input {
            width: 50px;
            height: 30px;
            text-align: center;
            margin: 0 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        
        .cart-summary {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .cart-summary-title {
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .cart-summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .cart-summary-total {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .cart-summary-total-amount {
            color: var(--primary-color);
        }
        
        .coupon-form {
            display: flex;
            margin-bottom: 20px;
        }
        
        .coupon-input {
            flex-grow: 1;
            margin-right: 10px;
        }
        
        .empty-cart {
            text-align: center;
            padding: 50px 0;
        }
        
        .empty-cart-icon {
            font-size: 5rem;
            color: #dee2e6;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Main Content -->
    <main class="container my-5">
        <h1 class="mb-4">Giỏ hàng</h1>
        
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../../index.html">Trang chủ</a></li>
                <li class="breadcrumb-item active" aria-current="page">Giỏ hàng</li>
            </ol>
        </nav>
        
        <div id="cart-container">
            <!-- Cart content will be loaded here via JavaScript -->
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Đang tải giỏ hàng...</p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.all.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/services/api-config.js"></script>
    <script src="../../assets/js/services/cart-service.js"></script>
    <script src="../../assets/js/common.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load cart
            loadCart();
        });
        
        // Load cart
        async function loadCart() {
            const cartContainer = document.getElementById('cart-container');
            const userId = localStorage.getItem('userId');
            
            if (!userId) {
                cartContainer.innerHTML = `
                    <div class="text-center py-5">
                        <div class="empty-cart-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h3>Vui lòng đăng nhập để xem giỏ hàng</h3>
                        <p class="mb-4">Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng</p>
                        <a href="../login/index.html" class="btn btn-primary">Đăng nhập</a>
                    </div>
                `;
                return;
            }

            try {
                cartContainer.innerHTML = `
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Đang tải giỏ hàng...</p>
                    </div>
                `;

                const cartItems = await CartService.getByUserId(userId);
                
                if (!cartItems || cartItems.length === 0) {
                    cartContainer.innerHTML = `
                        <div class="empty-cart">
                            <div class="empty-cart-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <h3>Giỏ hàng của bạn đang trống</h3>
                            <p class="mb-4">Hãy thêm sản phẩm vào giỏ hàng để tiếp tục mua sắm</p>
                            <a href="../product/index.html" class="btn btn-primary">Tiếp tục mua sắm</a>
                        </div>
                    `;
                    return;
                }

                // Calculate totals
                let subtotal = 0;
                cartItems.forEach(item => {
                    subtotal += item.price * item.quantity;
                });
                const shipping = cartItems.length > 0 ? 30000 : 0;
                const total = subtotal + shipping;

                // Render cart
                const cartHTML = `
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="cart-items">
                                ${cartItems.map(item => `
                                    <div class="cart-item">
                                        <div class="row align-items-center">
                                            <div class="col-md-2">
                                                <img src="${item.image}" alt="${item.productName}" class="cart-item-image">
                                            </div>
                                            <div class="col-md-4">
                                                <h5 class="cart-item-title">${item.productName}</h5>
                                                <div class="cart-item-price">${formatCurrency(item.price)}</div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="quantity-control">
                                                    <button class="quantity-btn" onclick="updateCartItemQuantity(${item.id}, ${item.quantity - 1})">-</button>
                                                    <input type="number" class="quantity-input" value="${item.quantity}" 
                                                        onchange="updateCartItemQuantity(${item.id}, this.value)" min="1">
                                                    <button class="quantity-btn" onclick="updateCartItemQuantity(${item.id}, ${item.quantity + 1})">+</button>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="cart-item-total">${formatCurrency(item.price * item.quantity)}</div>
                                            </div>
                                            <div class="col-md-1">
                                                <button class="btn btn-link text-danger" onclick="removeCartItem(${item.id})">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                            <div class="d-flex justify-content-between mt-4">
                                <a href="../product/index.html" class="btn btn-outline-primary">
                                    <i class="fas fa-arrow-left me-2"></i> Tiếp tục mua sắm
                                </a>
                                <button class="btn btn-outline-danger" onclick="clearCart()">
                                    <i class="fas fa-trash-alt me-2"></i> Xóa giỏ hàng
                                </button>
                            </div>
                        </div>
                        <div class="col-lg-4 mt-4 mt-lg-0">
                            <div class="cart-summary">
                                <h4 class="cart-summary-title">Tóm tắt đơn hàng</h4>
                                <div class="cart-summary-item">
                                    <span>Tạm tính</span>
                                    <span>${formatCurrency(subtotal)}</span>
                                </div>
                                <div class="cart-summary-item">
                                    <span>Phí vận chuyển</span>
                                    <span>${formatCurrency(shipping)}</span>
                                </div>
                                <div class="cart-summary-total">
                                    <span>Tổng cộng</span>
                                    <span class="cart-summary-total-amount">${formatCurrency(total)}</span>
                                </div>
                                <a href="../checkout/index.html" class="btn btn-primary w-100 mt-3">Tiến hành thanh toán</a>
                            </div>
                        </div>
                    </div>
                `;
                cartContainer.innerHTML = cartHTML;
            } catch (error) {
                console.error('Error loading cart:', error);
                cartContainer.innerHTML = `
                    <div class="alert alert-danger">
                        Không thể tải giỏ hàng. Vui lòng thử lại sau.
                    </div>
                `;
            }
        }
        
        // Update cart item quantity
        async function updateCartItemQuantity(itemId, newQuantity) {
            try {
                // Validate quantity
                newQuantity = parseInt(newQuantity);
                if (isNaN(newQuantity) || newQuantity < 1) {
                    newQuantity = 1;
                }

                showLoading();
                await CartService.updateItem(itemId, newQuantity);
                await loadCart();
                hideLoading();
                showToast('Giỏ hàng đã được cập nhật');
            } catch (error) {
                console.error('Error updating cart item:', error);
                hideLoading();
                showToast('Không thể cập nhật giỏ hàng. Vui lòng thử lại sau.', 'error');
            }
        }
        
        // Remove cart item
        async function removeCartItem(itemId) {
            try {
                const result = await Swal.fire({
                    title: 'Xác nhận',
                    text: 'Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Xóa',
                    cancelButtonText: 'Hủy'
                });

                if (result.isConfirmed) {
                    showLoading();
                    await CartService.removeItem(itemId);
                    await loadCart();
                    hideLoading();
                    showToast('Sản phẩm đã được xóa khỏi giỏ hàng');
                }
            } catch (error) {
                console.error('Error removing cart item:', error);
                hideLoading();
                showToast('Không thể xóa sản phẩm. Vui lòng thử lại sau.', 'error');
            }
        }
        
        // Clear cart
        async function clearCart() {
            try {
                const userId = localStorage.getItem('userId');
                if (!userId) {
                    showToast('Vui lòng đăng nhập để thực hiện thao tác này', 'error');
                    return;
                }

                const result = await Swal.fire({
                    title: 'Xác nhận',
                    text: 'Bạn có chắc chắn muốn xóa tất cả sản phẩm khỏi giỏ hàng?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Xóa tất cả',
                    cancelButtonText: 'Hủy'
                });

                if (result.isConfirmed) {
                    showLoading();
                    await CartService.clear(userId);
                    await loadCart();
                    hideLoading();
                    showToast('Giỏ hàng đã được xóa');
                }
            } catch (error) {
                console.error('Error clearing cart:', error);
                hideLoading();
                showToast('Không thể xóa giỏ hàng. Vui lòng thử lại sau.', 'error');
            }
        }
    </script>
</body>
</html>
