package com.project.codebasespringjpa.mapper;

import com.project.codebasespringjpa.dto.user.request.UserCreateRequest;
import com.project.codebasespringjpa.dto.user.request.UserUpdateRequest;
import com.project.codebasespringjpa.dto.user.response.UserResponse;
import com.project.codebasespringjpa.entity.RoleEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.util.ImageUtil;
import com.project.codebasespringjpa.util.UtilVariable;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class UserMapper {

    private final PasswordEncoder passwordEncoder;

    /**
     * <PERSON><PERSON><PERSON><PERSON> đổi từ UserEntity sang UserResponse
     */
    public UserResponse toResponse(UserEntity entity) {
        if (entity == null) {
            return null;
        }

        String avatarUrl = entity.getAvatar();
        if (avatarUrl == null || avatarUrl.isEmpty()) {
            avatarUrl = UtilVariable.IMAGE_DEFAULT; // Ảnh mặc định cho avatar
        }

        return UserResponse.builder()
                .id(entity.getId())
                .username(entity.getUsername())
                .email(entity.getEmail())
                .fullname(entity.getFullname())
                .phone(entity.getPhone())
                .avatar(ImageUtil.processImageUrl(avatarUrl))
                .address(entity.getAddress())
                .gender(entity.getGender())
                .roleName(entity.getRole() != null ? entity.getRole().getName() : null)
                .createDate(entity.getCreateDate())
                .createBy(entity.getCreateBy())
                .updateDate(entity.getUpdateDate())
                .updateBy(entity.getUpdateBy())
                .build();
    }

    /**
     * Chuyển đổi từ UserCreateRequest sang UserEntity
     */
    public UserEntity toEntity(UserCreateRequest request, RoleEntity role) {
        if (request == null) {
            return null;
        }

        return UserEntity.builder()
                .username(request.getUsername())
                .email(request.getEmail())
                .fullname(request.getFullname())
                .phone(request.getPhone())
                .avatar(request.getAvatar())
                .address(request.getAddress())
                .password(passwordEncoder.encode(request.getPassword()))
                .gender(request.getGender())
                .role(role)
                .build();
    }

    /**
     * Cập nhật UserEntity từ UserUpdateRequest
     */
    public void updateEntityFromRequest(UserEntity entity, UserUpdateRequest request) {
        if (entity == null || request == null) {
            return;
        }

        if (request.getUsername() != null) {
            entity.setUsername(request.getUsername());
        }

        if (request.getEmail() != null) {
            entity.setEmail(request.getEmail());
        }

        if (request.getFullname() != null) {
            entity.setFullname(request.getFullname());
        }

        if (request.getPhone() != null) {
            entity.setPhone(request.getPhone());
        }

        if (request.getAvatar() != null) {
            entity.setAvatar(request.getAvatar());
        }

        if (request.getAddress() != null) {
            entity.setAddress(request.getAddress());
        }

        if (request.getGender() != null) {
            entity.setGender(request.getGender());
        }
    }

    /**
     * Chuyển đổi danh sách UserEntity sang danh sách UserResponse
     */
    public List<UserResponse> toResponseList(List<UserEntity> entities) {
        if (entities == null) {
            return new ArrayList<>();
        }

        return entities.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }

    /**
     * Chuyển đổi Page<UserEntity> sang Page<UserResponse>
     */
    public Page<UserResponse> toResponsePage(Page<UserEntity> entityPage, Pageable pageable) {
        List<UserResponse> responseList = toResponseList(entityPage.getContent());
        return new PageImpl<>(responseList, pageable, entityPage.getTotalElements());
    }
}
