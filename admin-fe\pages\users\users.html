<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON><PERSON> l<PERSON> ng<PERSON> dùng - Tina Shop Admin</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.3/dist/sweetalert2.min.css">
  <!-- Common CSS -->
  <link rel="stylesheet" href="../../assets/css/common.css">
  <!-- Layout CSS -->
  <link rel="stylesheet" href="../../layout/header/header.css">
  <link rel="stylesheet" href="../../layout/sidebar/sidebar.css">
  <link rel="stylesheet" href="../../layout/footer/footer.css">
  <!-- Page CSS -->
  <link rel="stylesheet" href="users.css">
</head>
<body>
  <!-- Header -->
  <div id="header-container"></div>
  
  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <div id="sidebar-container"></div>
      
      <!-- Main content -->
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
          <h1 class="h2">Quản lý người dùng</h1>
          <div class="btn-toolbar mb-2 mb-md-0">
            <button type="button" class="btn btn-sm btn-primary me-2" id="btnAddUser">
              <i class="bi bi-plus-circle"></i> Thêm người dùng
            </button>
            <div class="btn-group me-2">
              <button type="button" class="btn btn-sm btn-outline-secondary">Xuất Excel</button>
              <button type="button" class="btn btn-sm btn-outline-secondary">In</button>
            </div>
          </div>
        </div>
        
        <!-- Bộ lọc -->
        <div class="row mb-3">
          <div class="col-md-12">
            <div class="card">
              <div class="card-body">
                <div class="row g-3">
                  <div hidden class="col-md-4">
                    <label for="filterRole" class="form-label">Vai trò</label>
                    <select class="form-select" id="filterRole">
                      <option value="">Tất cả vai trò</option>
                      <option value="ADMIN">Admin</option>
                      <option value="USER">Người dùng</option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label hidden for="filterStatus" class="form-label">Trạng thái</label>
                    <select hidden class="form-select" id="filterStatus">
                      <option value="">Tất cả trạng thái</option>
                      <option value="true">Hoạt động</option>
                      <option value="false">Bị khóa</option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label for="searchUser" class="form-label">Tìm kiếm</label>
                    <div class="input-group">
                      <input type="text" class="form-control" id="searchUser" placeholder="Tên, email, số điện thoại...">
                      <button class="btn btn-outline-secondary" type="button" id="btnSearch">
                        <i class="bi bi-search"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Danh sách người dùng -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped table-hover">
                    <thead>
                      <tr>
                        <th width="50">#</th>
                        <th width="80">Ảnh</th>
                        <th>Họ tên</th>
                        <th>Email</th>
                        <th>Số điện thoại</th>
                        <th>Vai trò</th>
                        <th width="150">Hành động</th>
                      </tr>
                    </thead>
                    <tbody id="userTable">
                      <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                      <tr>
                        <td colspan="8" class="text-center">Đang tải dữ liệu...</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                
                <!-- Phân trang -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                  <div>
                    <span id="totalUsers">0</span> người dùng
                  </div>
                  <div id="pagination" class="pagination-container">
                    <!-- Phân trang sẽ được thêm bằng JavaScript -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
  
  <!-- Footer -->
  <div id="footer-container"></div>
  
  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <!-- Axios -->
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.3/dist/sweetalert2.all.min.js"></script>
  <!-- Common JS -->
  <script src="../../assets/js/common.js"></script>
  <!-- Layout JS -->
  <script src="../../layout/header/header.js"></script>
  <script src="../../layout/sidebar/sidebar.js"></script>
  <!-- Page JS -->
  <script src="users.js"></script>
  
  <!-- Modal thêm người dùng -->
  <div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="userModalLabel">Thêm người dùng</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="userForm">
            <div class="mb-3">
              <label for="username" class="form-label">Tên đăng nhập</label>
              <input type="text" class="form-control" id="username" required>
            </div>
            <div class="mb-3">
              <label for="email" class="form-label">Email</label>
              <input type="email" class="form-control" id="email" required>
            </div>
            <div class="mb-3">
              <label for="fullname" class="form-label">Họ tên</label>
              <input type="text" class="form-control" id="fullname" required>
            </div>
            <div class="mb-3">
              <label for="phone" class="form-label">Số điện thoại</label>
              <input type="text" class="form-control" id="phone">
            </div>
            <div class="mb-3">
              <label for="avatar" class="form-label">Ảnh đại diện</label>
              <input type="file" class="form-control" id="avatar" accept="image/*">
              <div id="avatarPreview" class="mt-2"></div>
            </div>
            <div class="mb-3">
              <label for="address" class="form-label">Địa chỉ</label>
              <input type="text" class="form-control" id="address">
            </div>
            <div class="mb-3">
              <label for="password" class="form-label">Mật khẩu</label>
              <input type="password" class="form-control" id="password" required>
            </div>
            <div class="mb-3">
              <label for="gender" class="form-label">Giới tính</label>
              <select class="form-select" id="gender">
                <option value="">Chọn giới tính</option>
                <option value="MALE">Nam</option>
                <option value="FEMALE">Nữ</option>
                <option value="OTHER">Khác</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="roleName" class="form-label">Vai trò</label>
              <select class="form-select" id="roleName">
                <option value="CUSTOMER">Khách hàng</option>
                <option value="ADMIN">Admin</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="button" class="btn btn-primary" id="btnSaveUser">Lưu</button>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
