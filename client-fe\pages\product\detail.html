<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chi tiết sản phẩm - Tina Shop</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.min.css">
    <style>
        .product-images {
            position: relative;
        }
        
        .product-main-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        
        .product-thumbnails {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .product-thumbnail {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 5px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .product-thumbnail.active {
            border-color: var(--primary-color);
        }
        
        .product-discount-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: var(--primary-color);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: 600;
        }
        
        .product-info h1 {
            font-size: 1.8rem;
            margin-bottom: 15px;
        }
        
        .product-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .product-original-price {
            text-decoration: line-through;
            color: #6c757d;
            font-size: 1.1rem;
            margin-left: 10px;
        }
        
        .product-rating {
            margin-bottom: 15px;
        }
        
        .product-rating .rating-count {
            color: #6c757d;
            margin-left: 10px;
        }
        
        .product-description {
            margin-bottom: 20px;
        }
        
        .product-meta {
            margin-bottom: 20px;
        }
        
        .product-meta-item {
            margin-bottom: 10px;
        }
        
        .product-meta-label {
            font-weight: 600;
            margin-right: 10px;
        }
        
        .quantity-control {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .quantity-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 1.2rem;
        }
        
        .quantity-input {
            width: 60px;
            height: 40px;
            text-align: center;
            margin: 0 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        
        .product-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .product-share {
            margin-top: 20px;
        }
        
        .product-share a {
            margin-right: 15px;
            font-size: 1.2rem;
        }
        
        .product-tabs {
            margin-top: 50px;
        }
        
        .review-item {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        
        .review-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .review-author {
            font-weight: 600;
        }
        
        .review-date {
            color: #6c757d;
        }
        
        .related-products {
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Main Content -->
    <main class="container my-5">
        <div id="product-detail-container">
            <!-- Product details will be loaded here via JavaScript -->
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Đang tải thông tin sản phẩm...</p>
            </div>
        </div>
        
        <!-- Product Tabs -->
        <div class="product-tabs">
            <ul class="nav nav-tabs" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="description-tab" data-bs-toggle="tab" data-bs-target="#description" type="button" role="tab" aria-controls="description" aria-selected="true">Mô tả</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="specifications-tab" data-bs-toggle="tab" data-bs-target="#specifications" type="button" role="tab" aria-controls="specifications" aria-selected="false">Thông số kỹ thuật</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab" aria-controls="reviews" aria-selected="false">Đánh giá (<span id="reviewCount">0</span>)</button>
                </li>
            </ul>
            <div class="tab-content p-4 border border-top-0 rounded-bottom" id="productTabsContent">
                <div class="tab-pane fade show active" id="description" role="tabpanel" aria-labelledby="description-tab">
                    <div id="product-description">
                        <!-- Product description will be loaded here -->
                    </div>
                </div>
                <div class="tab-pane fade" id="specifications" role="tabpanel" aria-labelledby="specifications-tab">
                    <div id="product-specifications">
                        <!-- Product specifications will be loaded here -->
                    </div>
                </div>
                <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                    <div id="product-reviews">
                        <!-- Product reviews will be loaded here -->
                    </div>
                    
                    <!-- Review Form -->
                    <div class="review-form mt-4">
                        <h4>Viết đánh giá</h4>
                        <form id="reviewForm">
                            <div class="mb-3">
                                <label for="reviewRating" class="form-label">Đánh giá của bạn</label>
                                <div class="rating-select">
                                    <div class="btn-group" role="group" aria-label="Rating">
                                        <input type="radio" class="btn-check" name="rating" id="rating1" value="1" autocomplete="off">
                                        <label class="btn btn-outline-warning" for="rating1"><i class="fas fa-star"></i></label>
                                        
                                        <input type="radio" class="btn-check" name="rating" id="rating2" value="2" autocomplete="off">
                                        <label class="btn btn-outline-warning" for="rating2"><i class="fas fa-star"></i></label>
                                        
                                        <input type="radio" class="btn-check" name="rating" id="rating3" value="3" autocomplete="off">
                                        <label class="btn btn-outline-warning" for="rating3"><i class="fas fa-star"></i></label>
                                        
                                        <input type="radio" class="btn-check" name="rating" id="rating4" value="4" autocomplete="off">
                                        <label class="btn btn-outline-warning" for="rating4"><i class="fas fa-star"></i></label>
                                        
                                        <input type="radio" class="btn-check" name="rating" id="rating5" value="5" autocomplete="off" checked>
                                        <label class="btn btn-outline-warning" for="rating5"><i class="fas fa-star"></i></label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="reviewTitle" class="form-label">Tiêu đề</label>
                                <input type="text" class="form-control" id="reviewTitle" required>
                            </div>
                            <div class="mb-3">
                                <label for="reviewContent" class="form-label">Nội dung đánh giá</label>
                                <textarea class="form-control" id="reviewContent" rows="3" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Gửi đánh giá</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Related Products -->
        <div class="related-products">
            <h3 class="mb-4">Sản phẩm liên quan</h3>
            <div class="row" id="related-products-container">
                <!-- Related products will be loaded here -->
            </div>
        </div>
    </main>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.all.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/api.js"></script>
    <script src="../../assets/js/common.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get product ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const productId = urlParams.get('id');
            
            if (!productId) {
                // Redirect to products page if no ID provided
                window.location.href = 'index.html';
                return;
            }
            
            // Load product details
            loadProductDetails(productId);
            
            // Load related products
            loadRelatedProducts(productId);
            
            // Handle review form submission
            document.getElementById('reviewForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitReview(productId);
            });
        });
        
        // Load product details
        async function loadProductDetails(productId) {
            const productDetailContainer = document.getElementById('product-detail-container');
            try {
                const product = await apiService.getProductById(productId);
                // Generate product detail HTML
                const productDetailHTML = `
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="product-images">
                                <img src="${normalizeImageUrl(product.image)}" alt="${product.name}" class="product-main-image" id="mainProductImage">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="product-info">
                                <h1>${product.name}</h1>
                                <div class="product-price">
                                    ${formatCurrency(product.price)}
                                </div>
                                <div class="product-description">
                                    <p>${product.description || ''}</p>
                                </div>
                                <div class="product-meta">
                                    <div class="product-meta-item">
                                        <span class="product-meta-label">Danh mục:</span>
                                        <span>${product.categoryName || ''}</span>
                                    </div>
                                    <div class="product-meta-item">
                                        <span class="product-meta-label">Nhà cung cấp:</span>
                                        <span>${product.supplierName || ''}</span>
                                    </div>
                                    <div class="product-meta-item">
                                        <span class="product-meta-label">Số lượng:</span>
                                        <span>${product.quantity || 0}</span>
                                    </div>
                                </div>
                                <div class="quantity-control">
                                    <button class="quantity-btn" onclick="decreaseQuantity()">-</button>
                                    <input type="number" class="quantity-input" id="productQuantity" value="1" min="1">
                                    <button class="quantity-btn" onclick="increaseQuantity()">+</button>
                                </div>
                                <div class="product-actions">
                                    <button class="btn btn-primary btn-lg" onclick="addToCartFromDetail(${product.id})">
                                        <i class="fas fa-shopping-cart me-2"></i> Thêm vào giỏ hàng
                                    </button>
                                    <button class="btn btn-outline-danger btn-lg" onclick="addToWishlist(${product.id})">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                productDetailContainer.innerHTML = productDetailHTML;
                document.title = product.name + ' - Tina Shop';
            } catch (error) {
                productDetailContainer.innerHTML = `<div class='alert alert-danger'>Không thể tải chi tiết sản phẩm.</div>`;
            }
        }
        
        // Load related products
        function loadRelatedProducts(productId) {
            const relatedProductsContainer = document.getElementById('related-products-container');
            
            // In a real application, you would call your API
            // For now, we'll simulate an API call with mock data
            setTimeout(() => {
                // Mock related products data
                const relatedProducts = [
                    {
                        id: 2,
                        name: 'Ví cầm tay nữ da thật',
                        price: 850000,
                        salePrice: 680000,
                        imageUrl: '../../assets/image/product2.jpg',
                        rating: 5
                    },
                    {
                        id: 3,
                        name: 'Túi đeo chéo mini phong cách Hàn Quốc',
                        price: 750000,
                        imageUrl: '../../assets/image/product3.jpg',
                        rating: 4
                    },
                    {
                        id: 4,
                        name: 'Bộ phụ kiện thời trang cao cấp',
                        price: 1500000,
                        salePrice: 1200000,
                        imageUrl: '../../assets/image/product4.jpg',
                        rating: 4.5
                    },
                    {
                        id: 5,
                        name: 'Túi xách công sở nữ',
                        price: 1100000,
                        imageUrl: '../../assets/image/product5.jpg',
                        rating: 4.2
                    }
                ];
                
                // Filter out current product
                const filteredProducts = relatedProducts.filter(product => product.id != productId);
                
                // Generate related products HTML
                relatedProductsContainer.innerHTML = '';
                
                filteredProducts.slice(0, 4).forEach(product => {
                    const productCard = document.createElement('div');
                    productCard.className = 'col-md-3 col-sm-6 mb-4';
                    
                    const discountPercent = product.salePrice ? Math.round((1 - product.salePrice / product.price) * 100) : 0;
                    
                    productCard.innerHTML = `
                        <div class="product-card">
                            <div class="product-img-container">
                                <img src="${product.imageUrl}" alt="${product.name}" class="product-img">
                                ${discountPercent > 0 ? `<span class="badge bg-danger position-absolute top-0 start-0 m-2">-${discountPercent}%</span>` : ''}
                            </div>
                            <div class="product-info">
                                <h5 class="product-title">
                                    <a href="detail.html?id=${product.id}">${product.name}</a>
                                </h5>
                                <div class="product-rating">
                                    ${createRatingStars(product.rating)}
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        ${product.salePrice ? 
                                            `<span class="product-price">${formatCurrency(product.salePrice)}</span>
                                            <small class="text-muted text-decoration-line-through">${formatCurrency(product.price)}</small>` : 
                                            `<span class="product-price">${formatCurrency(product.price)}</span>`
                                        }
                                    </div>
                                    <button class="btn btn-sm btn-primary" onclick="addToCart(${product.id})">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    relatedProductsContainer.appendChild(productCard);
                });
            }, 500);
        }
        
        // Submit review
        function submitReview(productId) {
            const rating = document.querySelector('input[name="rating"]:checked').value;
            const title = document.getElementById('reviewTitle').value;
            const content = document.getElementById('reviewContent').value;
            
            // In a real application, you would call your API to submit the review
            // For now, we'll just show a success message
            
            Swal.fire({
                title: 'Cảm ơn bạn!',
                text: 'Đánh giá của bạn đã được gửi thành công và đang chờ phê duyệt.',
                icon: 'success',
                confirmButtonText: 'OK'
            }).then(() => {
                // Reset form
                document.getElementById('reviewForm').reset();
            });
        }
        
        // Change main product image
        function changeMainImage(imageSrc, thumbnailElement) {
            document.getElementById('mainProductImage').src = imageSrc;
            
            // Update active thumbnail
            document.querySelectorAll('.product-thumbnail').forEach(thumbnail => {
                thumbnail.classList.remove('active');
            });
            thumbnailElement.classList.add('active');
        }
        
        // Increase quantity
        function increaseQuantity() {
            const quantityInput = document.getElementById('productQuantity');
            quantityInput.value = parseInt(quantityInput.value) + 1;
        }
        
        // Decrease quantity
        function decreaseQuantity() {
            const quantityInput = document.getElementById('productQuantity');
            const currentValue = parseInt(quantityInput.value);
            if (currentValue > 1) {
                quantityInput.value = currentValue - 1;
            }
        }
        
        // Add to cart from detail page
        function addToCartFromDetail(productId) {
            const quantity = parseInt(document.getElementById('productQuantity').value);
            addToCart(productId, quantity);
        }

        // Helper
        function normalizeImageUrl(url) {
            if (!url) return '../../assets/image/default-product.jpg';
            if (url.startsWith('http')) return url;
            return 'http://localhost:8080/' + url.replace(/^\/+/, '');
        }
    </script>
</body>
</html>
