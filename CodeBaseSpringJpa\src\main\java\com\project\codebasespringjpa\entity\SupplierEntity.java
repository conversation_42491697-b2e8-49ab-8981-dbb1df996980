package com.project.codebasespringjpa.entity;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "tbl_supplier")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SupplierEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @Column(name = "name", nullable = false)
    String name;

    @Column(name = "email")
    String email;

    @Column(name = "phone")
    String phone;

    @Column(name = "address", columnDefinition = "TEXT")
    String address;

    @Column(name = "description", columnDefinition = "TEXT")
    String description;

    @Column(name = "status")
    Boolean status = true;

    @OneToMany(mappedBy = "supplier", cascade = CascadeType.ALL)
    List<ProductEntity> products;

}
