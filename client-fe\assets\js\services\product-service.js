// Product Service for Client

// Product API
const ProductService = {
    getAll: async (page = 0, size = 12) => {
        return await apiRequest(`products?page=${page}&size=${size}`);
    },
    
    getById: async (id) => {
        return await apiRequest(`products/${id}`);
    },
    
    getBySlug: async (slug) => {
        return await apiRequest(`products/slug/${slug}`);
    },
    
    getByCategoryId: async (categoryId, page = 0, size = 12) => {
        return await apiRequest(`products/category/${categoryId}?page=${page}&size=${size}`);
    },
    
    getByFeatured: async (featured = true, page = 0, size = 8) => {
        return await apiRequest(`products/featured/${featured}?page=${page}&size=${size}`);
    },
    
    search: async (keyword, page = 0, size = 12) => {
        return await apiRequest(`products/search?keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`);
    },
    
    getVariants: async (productId) => {
        return await apiRequest(`product-variants/product/${productId}`);
    }
};
