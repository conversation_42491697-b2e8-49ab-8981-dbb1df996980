package com.project.codebasespringjpa.controller;

import com.project.codebasespringjpa.service.interfaces.IOrderDetailService;
import com.project.codebasespringjpa.service.interfaces.IOrderService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/statistics")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class StatisticsController {
    IOrderService orderService;
    IOrderDetailService orderDetailService;

    @GetMapping("/revenue")
    public ResponseEntity<Map<String, Object>> getRevenueStatistics(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        Map<String, Object> statistics = new HashMap<>();
        
        // Tổng doanh thu
        Double totalRevenue = orderService.getTotalRevenue();
        statistics.put("totalRevenue", totalRevenue);
        
        // Doanh thu trong khoảng thời gian
        if (startDate != null && endDate != null) {
            Double revenueBetween = orderService.getRevenueBetween(startDate, endDate);
            statistics.put("revenueBetween", revenueBetween);
            statistics.put("startDate", startDate);
            statistics.put("endDate", endDate);
        }
        
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/orders")
    public ResponseEntity<Map<String, Object>> getOrderStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // Số lượng đơn hàng theo trạng thái
        statistics.put("pendingOrders", orderService.countByStatus("PENDING"));
        statistics.put("processingOrders", orderService.countByStatus("PROCESSING"));
        statistics.put("shippedOrders", orderService.countByStatus("SHIPPED"));
        statistics.put("deliveredOrders", orderService.countByStatus("DELIVERED"));
        statistics.put("cancelledOrders", orderService.countByStatus("CANCELLED"));
        
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/top-selling-products")
    public ResponseEntity<List<Map<String, Object>>> getTopSellingProducts() {
        return ResponseEntity.ok(orderDetailService.findTopSellingProducts());
    }
}
