<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="081f8add-68bb-4ae3-8257-5d54d86b1921" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Enum" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2wU6YxeYWu6KQACZyzypsntB8yN" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.CodeBaseSpringJpaApplication.executor": "Run",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "E:/zzz_coding/alpha_priority/alpha_01_du_an_tina_shop/shop_tina/CodeBaseSpringJpa",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\zzz_coding\alpha_working\shop_tina\admin\CodeBaseSpringJpa\src\main\resources\static" />
      <recent name="E:\zzz_coding\alpha_working\shop_tina\admin\CodeBaseSpringJpa\src\main\resources\static\uploads" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="CodeBaseSpringJpaApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="CodeBaseSpringJpa" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.project.codebasespringjpa.CodeBaseSpringJpaApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="081f8add-68bb-4ae3-8257-5d54d86b1921" name="Changes" comment="" />
      <created>1746083857633</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746083857633</updated>
      <workItem from="1746083858841" duration="10097000" />
      <workItem from="1746100429037" duration="11711000" />
      <workItem from="1746335533101" duration="24000" />
      <workItem from="1747705373843" duration="1088000" />
      <workItem from="1747708664715" duration="313000" />
      <workItem from="1747709004627" duration="6315000" />
      <workItem from="1747722566192" duration="7346000" />
      <workItem from="1747733106082" duration="6138000" />
      <workItem from="1747843983816" duration="691000" />
      <workItem from="1748364694766" duration="26000" />
      <workItem from="1748852008736" duration="1414000" />
      <workItem from="1748854826658" duration="1022000" />
      <workItem from="1748871186345" duration="20701000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/project/codebasespringjpa/service/imp/AuthenService.java</url>
          <line>65</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>