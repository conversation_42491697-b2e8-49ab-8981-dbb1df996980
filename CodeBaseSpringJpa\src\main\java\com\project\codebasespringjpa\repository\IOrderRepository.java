package com.project.codebasespringjpa.repository;

import com.project.codebasespringjpa.entity.OrderEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface IOrderRepository extends JpaRepository<OrderEntity, Long> {

    @Query("SELECT COUNT(o) FROM OrderEntity o WHERE o.status = ?1")
    Long countByStatus(String status);
    
    @Query("SELECT SUM(o.totalAmount) FROM OrderEntity o WHERE o.status = 'DELIVERED'")
    Double getTotalRevenue();
    
    @Query("SELECT SUM(o.totalAmount) FROM OrderEntity o WHERE o.status = 'DELIVERED' ")
    Double getRevenueBetween(LocalDateTime startDate, LocalDateTime endDate);

    @Query("""
    select ord from OrderEntity ord where 
    (:userid is null or ord.user.id = :userid) 
    and (:status is null or ord.status = :status)  
    and (:keyword is null or ord.user.username like concat('%', :keyword, '%') ) 
    and (:startDate is null or date(ord.createDate) >= :startDate) 
    and (:endDate is null or date(ord.createDate) <= :endDate) 
    order by ord.createDate desc 
""")
    Page<OrderEntity> findAll(@Param("userid") Long userId,
                              @Param("status") String status,
                              @Param("keyword") String keyword,
                              @Param("startDate") LocalDate start,
                              @Param("endDate") LocalDate end,
                              Pageable pageable);
}
