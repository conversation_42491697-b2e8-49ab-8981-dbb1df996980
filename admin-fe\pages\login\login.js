document.addEventListener('DOMContentLoaded', function() {
  // <PERSON><PERSON>m tra nếu đã đăng nhập thì chuyển hướng đến trang dashboard
  const token = localStorage.getItem('token');
  if (token) {
    window.location.href = '../dashboard/dashboard.html';
  }

  // X<PERSON> lý form đăng nhập
  const loginForm = document.getElementById('loginForm');

  loginForm.addEventListener('submit', function(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    // Gọi API đăng nhập
    axios.post('http://localhost:8080/auth/login', {
      username: username,
      password: password
    })
    .then(function(response) {
      // Lưu token vào localStorage
      localStorage.setItem('token', response.data.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.data.user));

      // Hi<PERSON><PERSON> thị thông báo thành công
      Swal.fire({
        icon: 'success',
        title: '<PERSON>ăng nhập thành công!',
        text: 'Đang chuyển hướng đến trang quản trị...',
        timer: 1500,
        showConfirmButton: false
      }).then(() => {
        // Chuyển hướng đến trang dashboard
        window.location.href = '/pages/dashboard/dashboard.html';
      });
    })
    .catch(function(error) {
      console.error('Login error:', error);

      // Hiển thị thông báo lỗi
      let errorMessage = 'Đã xảy ra lỗi khi đăng nhập';

      if (error.response) {
        // Lỗi từ server
        errorMessage = error.response.data.message || errorMessage;
      } else if (error.request) {
        // Không nhận được phản hồi từ server
        errorMessage = 'Không thể kết nối đến server';
      }

      Swal.fire({
        icon: 'error',
        title: 'Đăng nhập thất bại',
        text: errorMessage
      });
    });
  });
});
