package com.project.codebasespringjpa.service.interfaces;

import com.project.codebasespringjpa.dto.product.request.ProductRequest;
import com.project.codebasespringjpa.dto.product.request.ProductSearch;
import com.project.codebasespringjpa.dto.product.response.ProductResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface IProductService {
    Page<ProductResponse> findAll(ProductSearch productSearch, Pageable pageable);
    ProductResponse findById(Long id);
    ProductResponse create(ProductRequest request);
    ProductResponse update(Long id, ProductRequest request);
    void delete(Long id);
    boolean existsByName(String name);
}
