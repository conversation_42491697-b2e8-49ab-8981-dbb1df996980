package com.project.codebasespringjpa.dto.report;

import com.project.codebasespringjpa.dto.product.response.ProductResponse;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class InventoryReportResponse {
    InventoryOverview overview;
    List<StockByCategory> stockByCategory;
    List<ValueByCategory> valueByCategory;
    Page<ProductResponse> products;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InventoryOverview {
        int totalProducts;
        int totalStock;
        int lowStockProducts;
        BigDecimal inventoryValue;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class StockByCategory {
        String categoryName;
        int stock;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ValueByCategory {
        String categoryName;
        BigDecimal value;
    }
}
