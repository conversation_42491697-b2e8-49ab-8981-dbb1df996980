package com.project.codebasespringjpa.dto.order.response;

import com.project.codebasespringjpa.dto.product.response.ProductResponse;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderResponse {
    Long id;
    Long userId;
    String username;
    String phone;

    LocalDateTime orderDate;
    Double totalAmount;
    String status;
    String shippingAddress;
    String paymentMethod;
    String paymentStatus;
    String note;

    List<OrderDetailResponse> orderDetails;
}
