package com.project.codebasespringjpa.repository;

import com.project.codebasespringjpa.dto.product.response.ProductResponse;
import com.project.codebasespringjpa.entity.CategoryEntity;
import com.project.codebasespringjpa.entity.ProductEntity;
import com.project.codebasespringjpa.entity.SupplierEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IProductRepository extends JpaRepository<ProductEntity, Long> {

    Page<ProductEntity> findByCategoryId(Long categoryId, Pageable pageable);
    Page<ProductEntity> findBySupplierId(Long supplierId, Pageable pageable);
    Page<ProductEntity> findByCategoryIdAndSupplierId(Long categoryId, Long supplierId, Pageable pageable);

    @Query("SELECT p FROM ProductEntity p WHERE p.name LIKE CONCAT('%', :keyword, '%') OR p.description LIKE CONCAT('%', :keyword, '%')")
    Page<ProductEntity> search(@Param("keyword") String keyword, Pageable pageable);

    @Query("""
        select pr from ProductEntity pr where pr.isDelete = false 
        and (:keyword is null or pr.name like concat('%', :keyword, '%') ) 
        and (:category is null or pr.category.name like concat('%', :category, '%') ) 
        and (:suplier is null or pr.supplier.name like concat('%', :suplier, '%') )    
        and (:categoryId is null or pr.category.id = :categoryId)     
        and (:suplierId is null or pr.supplier.id = :suplierId)   
        order by pr.createDate desc 
    """)
    Page<ProductEntity> findAll(@Param("keyword") String keyword,
                                @Param("category") String category,
                                @Param("suplier") String suplier,
                                @Param("categoryId") Long categoryId,
                                @Param("suplierId") Long suplierId,
                                Pageable pageable);


    boolean existsByName(String name);
}
