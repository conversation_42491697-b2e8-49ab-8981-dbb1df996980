package com.project.codebasespringjpa.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum PaymentStatusEnum {
    PENDING,
    PAID,
    FAILED;

    public static List<String> statusList() {
        List<String> statusList = Arrays.stream(PaymentStatusEnum.values())
                .map(Enum::name)
                .collect(Collectors.toList());

        return statusList;
    }
}
