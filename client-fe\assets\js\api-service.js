// API Service for Client
const API_BASE_URL = 'http://localhost:8080/api';

// Authentication token handling
function getAuthToken() {
    return localStorage.getItem('userToken');
}

function setAuthToken(token) {
    localStorage.setItem('userToken', token);
}

function removeAuthToken() {
    localStorage.removeItem('userToken');
}

// Common headers with authentication
function getHeaders() {
    const headers = {
        'Content-Type': 'application/json'
    };
    
    const token = getAuthToken();
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    
    return headers;
}

// Generic API request function
async function apiRequest(endpoint, method = 'GET', data = null) {
    try {
        const url = `${API_BASE_URL}/${endpoint}`;
        const options = {
            method,
            headers: getHeaders()
        };
        
        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        
        // Handle 401 Unauthorized (token expired or invalid)
        if (response.status === 401 && getAuthToken()) {
            removeAuthToken();
            // Only redirect if we're not already on the login page
            if (!window.location.href.includes('/login/')) {
                window.location.href = '../../pages/login/index.html';
            }
            throw new Error('Unauthorized. Please login again.');
        }
        
        // Parse response
        const responseData = await response.json();
        
        // Check if response indicates an error
        if (!response.ok) {
            throw new Error(responseData.message || 'API request failed');
        }
        
        return responseData;
    } catch (error) {
        console.error('API request error:', error);
        throw error;
    }
}

// Authentication API
const AuthAPI = {
    login: async (email, password) => {
        try {
            const response = await apiRequest('auth/login', 'POST', { email, password });
            if (response.accessToken) {
                setAuthToken(response.accessToken);
            }
            return response;
        } catch (error) {
            throw error;
        }
    },
    
    register: async (userData) => {
        return await apiRequest('auth/register', 'POST', userData);
    },
    
    logout: () => {
        removeAuthToken();
    }
};

// Category API
const CategoryAPI = {
    getAll: async () => {
        return await apiRequest('categories');
    },
    
    getById: async (id) => {
        return await apiRequest(`categories/${id}`);
    },
    
    getBySlug: async (slug) => {
        return await apiRequest(`categories/slug/${slug}`);
    },
    
    getMainCategories: async () => {
        return await apiRequest('categories/main');
    }
};

// Product API
const ProductAPI = {
    getAll: async (page = 0, size = 12) => {
        return await apiRequest(`products?page=${page}&size=${size}`);
    },
    
    getById: async (id) => {
        return await apiRequest(`products/${id}`);
    },
    
    getBySlug: async (slug) => {
        return await apiRequest(`products/slug/${slug}`);
    },
    
    getByCategoryId: async (categoryId, page = 0, size = 12) => {
        return await apiRequest(`products/category/${categoryId}?page=${page}&size=${size}`);
    },
    
    getByFeatured: async (featured = true, page = 0, size = 8) => {
        return await apiRequest(`products/featured/${featured}?page=${page}&size=${size}`);
    },
    
    search: async (keyword, page = 0, size = 12) => {
        return await apiRequest(`products/search?keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`);
    },
    
    getVariants: async (productId) => {
        return await apiRequest(`product-variants/product/${productId}`);
    }
};

// Blog API
const BlogAPI = {
    getAll: async (page = 0, size = 10) => {
        return await apiRequest(`blogs?page=${page}&size=${size}`);
    },
    
    getById: async (id) => {
        return await apiRequest(`blogs/${id}`);
    },
    
    getBySlug: async (slug) => {
        return await apiRequest(`blogs/slug/${slug}`);
    },
    
    search: async (keyword, page = 0, size = 10) => {
        return await apiRequest(`blogs/search?keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`);
    },
    
    getTopViewed: async (limit = 5) => {
        return await apiRequest(`blogs/top-viewed?limit=${limit}`);
    }
};

// Review API
const ReviewAPI = {
    getByProductId: async (productId) => {
        return await apiRequest(`reviews/product/${productId}`);
    },
    
    create: async (reviewData) => {
        return await apiRequest('reviews', 'POST', reviewData);
    }
};

// Cart API
const CartAPI = {
    getByUserId: async (userId) => {
        return await apiRequest(`carts/user/${userId}`);
    },
    
    addItem: async (userId, cartItemData) => {
        return await apiRequest(`carts/${userId}/items`, 'POST', cartItemData);
    },
    
    updateItem: async (userId, itemId, quantity) => {
        return await apiRequest(`carts/${userId}/items/${itemId}`, 'PUT', { quantity });
    },
    
    removeItem: async (userId, itemId) => {
        return await apiRequest(`carts/${userId}/items/${itemId}`, 'DELETE');
    },
    
    clear: async (userId) => {
        return await apiRequest(`carts/${userId}/clear`, 'DELETE');
    }
};

// Wishlist API
const WishlistAPI = {
    getByUserId: async (userId) => {
        return await apiRequest(`wishlists/user/${userId}`);
    },
    
    add: async (wishlistData) => {
        return await apiRequest('wishlists', 'POST', wishlistData);
    },
    
    remove: async (id) => {
        return await apiRequest(`wishlists/${id}`, 'DELETE');
    },
    
    check: async (userId, productId) => {
        return await apiRequest(`wishlists/check?userId=${userId}&productId=${productId}`);
    }
};

// Order API
const OrderAPI = {
    getByUserId: async (userId) => {
        return await apiRequest(`orders/user/${userId}`);
    },
    
    getById: async (id) => {
        return await apiRequest(`orders/${id}`);
    },
    
    create: async (orderData) => {
        return await apiRequest('orders', 'POST', orderData);
    }
};

// Coupon API
const CouponAPI = {
    validate: async (code, amount) => {
        return await apiRequest(`coupons/validate?code=${code}&amount=${amount}`);
    }
};

// User API
const UserAPI = {
    getProfile: async () => {
        return await apiRequest('users/profile');
    },
    
    updateProfile: async (userData) => {
        return await apiRequest('users/profile', 'PUT', userData);
    },
    
    changePassword: async (passwordData) => {
        return await apiRequest('users/change-password', 'PUT', passwordData);
    }
};
