package com.project.codebasespringjpa.dto.order.request;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderRequest {
    Long userId;

    String username;
    String phone;

    Double totalAmount;
    String status;

    String shippingAddress;

    String paymentMethod;
    String paymentStatus;
    String note;
    List<OrderDetailRequest> orders;
}
