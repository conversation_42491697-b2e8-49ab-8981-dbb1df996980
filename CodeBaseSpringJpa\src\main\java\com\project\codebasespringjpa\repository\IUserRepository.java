package com.project.codebasespringjpa.repository;

import com.project.codebasespringjpa.entity.UserEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IUserRepository extends JpaRepository<UserEntity, Long> {
    Optional<UserEntity> findByUsername(String username);
    Optional<UserEntity> findByEmail(String email);
    Boolean existsByUsername(String username);
    Boolean existsByEmail(String email);

    // <PERSON><PERSON>c phương thức tìm kiếm chỉ lấy người dùng chưa bị xóa
    Optional<UserEntity> findByUsernameAndIsDeleteFalse(String username);
    Optional<UserEntity> findByEmailAndIsDeleteFalse(String email);
    Optional<UserEntity> findByIdAndIsDeleteFalse(Long id);
    List<UserEntity> findByIsDeleteFalse();
    Page<UserEntity> findByIsDeleteFalse(Pageable pageable);

    @Query("SELECT u FROM UserEntity u WHERE " +
            "(LOWER(u.username) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(u.email) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(u.fullname) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(u.phone) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
            "AND u.isDelete = false")
    Page<UserEntity> searchActiveUsers(@Param("keyword") String keyword, Pageable pageable);
}
