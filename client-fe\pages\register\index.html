<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đăng ký - <PERSON> Shop</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.min.css">
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Main Content -->
    <main class="container my-5">
        <div class="auth-container">
            <h2 class="text-center mb-4">Đăng ký tài khoản</h2>
            
            <form id="registerForm">
                <div class="mb-3">
                    <label for="fullName" class="form-label">Họ và tên</label>
                    <input type="text" class="form-control" id="fullName" required>
                </div>
                <div class="mb-3">
                    <label for="email" class="form-label">Email hoặc tên đăng nhập</label>
                    <input type="text" class="form-control" id="email" required>
                </div>
                <div class="mb-3">
                    <label for="phone" class="form-label">Số điện thoại</label>
                    <input type="tel" class="form-control" id="phone" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Mật khẩu</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="password" required>
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="far fa-eye"></i>
                        </button>
                    </div>
                    <div class="form-text">
                        Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số.
                    </div>
                </div>
                <div class="mb-3">
                    <label for="confirmPassword" class="form-label">Xác nhận mật khẩu</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="confirmPassword" required>
                        <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                            <i class="far fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                    <label class="form-check-label" for="agreeTerms">
                        Tôi đồng ý với <a href="../policy/terms.html" target="_blank">Điều khoản dịch vụ</a> và <a href="../policy/privacy.html" target="_blank">Chính sách bảo mật</a>
                    </label>
                </div>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">Đăng ký</button>
                </div>
            </form>
            
            <hr class="my-4">
            
            <div class="text-center">
                <p>Đã có tài khoản? <a href="../login/index.html">Đăng nhập ngay</a></p>
            </div>
            
            <div class="mt-4">
                <p class="text-center mb-3">Hoặc đăng ký với</p>
                <div class="d-flex justify-content-center gap-3">
                    <button class="btn btn-outline-primary" id="facebookRegister">
                        <i class="fab fa-facebook-f me-2"></i> Facebook
                    </button>
                    <button class="btn btn-outline-danger" id="googleRegister">
                        <i class="fab fa-google me-2"></i> Google
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.all.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/services/api-config.js"></script>
    <script src="../../assets/js/services/auth-service.js"></script>
    <script src="../../assets/js/common.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle password visibility
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });
            
            // Toggle confirm password visibility
            const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            
            toggleConfirmPassword.addEventListener('click', function() {
                const type = confirmPasswordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                confirmPasswordInput.setAttribute('type', type);
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });
            
            // Handle register form submission
            const registerForm = document.getElementById('registerForm');
            
            registerForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const fullName = document.getElementById('fullName').value;
                const email = document.getElementById('email').value;
                const phone = document.getElementById('phone').value;
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                const agreeTerms = document.getElementById('agreeTerms').checked;
                
                // Validate form
                if (password !== confirmPassword) {
                    Swal.fire({
                        title: 'Lỗi',
                        text: 'Mật khẩu xác nhận không khớp',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    return;
                }
                
                if (!agreeTerms) {
                    Swal.fire({
                        title: 'Lỗi',
                        text: 'Bạn phải đồng ý với Điều khoản dịch vụ và Chính sách bảo mật',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    return;
                }
                
                // Validate password strength
                const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
                if (!passwordRegex.test(password)) {
                    Swal.fire({
                        title: 'Lỗi',
                        text: 'Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    return;
                }
                
                try {
                    showLoading();
                    
                    // Prepare user data
                    const userData = {
                        fullname: fullName,
                        email,
                        phone,
                        password,
                        username: email,
                        address: ''
                    };

                    // Call register API
                    const response = await AuthService.register(userData);
                    
                    hideLoading();
                    
                    // Show success message
                    Swal.fire({
                        title: 'Đăng ký thành công!',
                        text: 'Vui lòng đăng nhập để tiếp tục',
                        icon: 'success',
                        confirmButtonText: 'Đăng nhập ngay'
                    }).then(() => {
                        // Redirect to login page
                        window.location.href = '../login/index.html';
                    });
                } catch (error) {
                    hideLoading();

                    Swal.fire({
                        title: 'Đăng ký thất bại',
                        text: error.message || 'Đã xảy ra lỗi khi đăng ký',
                        icon: 'error',
                        confirmButtonText: 'Thử lại'
                    });
                }
            });
            
            // Handle social registrations
            document.getElementById('facebookRegister').addEventListener('click', function() {
                // Implement Facebook registration
                alert('Chức năng đăng ký bằng Facebook sẽ được triển khai sau');
            });
            
            document.getElementById('googleRegister').addEventListener('click', function() {
                // Implement Google registration
                alert('Chức năng đăng ký bằng Google sẽ được triển khai sau');
            });
        });
    </script>
</body>
</html>
