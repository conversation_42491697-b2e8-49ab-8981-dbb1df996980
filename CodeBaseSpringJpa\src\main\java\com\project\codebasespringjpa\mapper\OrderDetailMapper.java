package com.project.codebasespringjpa.mapper;

import com.project.codebasespringjpa.dto.order.request.OrderDetailRequest;
import com.project.codebasespringjpa.dto.order.response.OrderDetailResponse;
import com.project.codebasespringjpa.entity.OrderDetailEntity;
import com.project.codebasespringjpa.entity.OrderEntity;
import com.project.codebasespringjpa.entity.ProductEntity;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderDetailMapper {
    public OrderDetailEntity toEntity(OrderDetailRequest request){
        ProductEntity product = ProductEntity.builder()
                .id(request.getProductId())
                .build();

        OrderEntity order = OrderEntity.builder()
                .id(request.getOrderId())
                .build();

        return OrderDetailEntity.builder()
                .order(order)
                .product(product)
                .quantity(request.getQuantity())
                .price(request.getPrice())
                .build();
    }

    public OrderDetailResponse toResponse(OrderDetailEntity entity){
        return OrderDetailResponse.builder()
                .id(entity.getId())
                .orderId(entity.getOrder().getId())

                .idProduct(entity.getProduct().getId())
                .productName(entity.getProduct().getName())
                .image(entity.getProduct().getImage())

                .quantity(entity.getQuantity())
                .price(entity.getPrice())
                .build();
    }

}
