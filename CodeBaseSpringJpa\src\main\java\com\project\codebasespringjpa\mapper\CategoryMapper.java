package com.project.codebasespringjpa.mapper;

import com.project.codebasespringjpa.dto.category.request.CategoryCreateRequest;
import com.project.codebasespringjpa.dto.category.request.CategoryUpdateRequest;
import com.project.codebasespringjpa.dto.category.response.CategoryResponse;
import com.project.codebasespringjpa.entity.CategoryEntity;
import com.project.codebasespringjpa.util.ImageUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class CategoryMapper {

    /**
     * <PERSON>yển đổi từ CategoryEntity sang CategoryResponse
     */
    public CategoryResponse toResponse(CategoryEntity entity) {
        if (entity == null) {
            return null;
        }

        return CategoryResponse.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .image(ImageUtil.processImageUrl(entity.getImage()))
                .createDate(entity.getCreateDate())
                .createBy(entity.getCreateBy())
                .updateDate(entity.getUpdateDate())
                .updateBy(entity.getUpdateBy())
                .build();
    }



    /**
     * Chuyển đổi từ CategoryCreateRequest sang CategoryEntity
     */
    public CategoryEntity toEntity(CategoryCreateRequest request) {
        if (request == null) {
            return null;
        }

        return CategoryEntity.builder()
                .name(request.getName())
                .description(request.getDescription())
                .image(request.getImage())
                .build();
    }

    /**
     * Cập nhật CategoryEntity từ CategoryUpdateRequest
     */
    public void updateEntityFromRequest(CategoryEntity entity, CategoryUpdateRequest request) {
        if (entity == null || request == null) {
            return;
        }

        if (request.getName() != null) {
            entity.setName(request.getName());
        }

        if (request.getDescription() != null) {
            entity.setDescription(request.getDescription());
        }

        if (request.getImage() != null) {
            entity.setImage(request.getImage());
        }


    }

    /**
     * Chuyển đổi danh sách CategoryEntity sang danh sách CategoryResponse
     */
    public List<CategoryResponse> toResponseList(List<CategoryEntity> entities) {
        if (entities == null) {
            return new ArrayList<>();
        }

        return entities.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }
}
