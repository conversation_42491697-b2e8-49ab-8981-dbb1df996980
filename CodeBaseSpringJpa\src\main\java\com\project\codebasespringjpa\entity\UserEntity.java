package com.project.codebasespringjpa.entity;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.hibernate.internal.build.AllowNonPortable;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "tbl_user")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserEntity extends BaseEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @Column(name = "username", unique = true)
    String username;

    @Column(name = "email", unique = true)
    String email;

    @Column(name = "fullname")
    String fullname;

    @Column(name = "phone")
    String phone;

    @Column(name = "avatar")
    String avatar;

    @Column(name = "address", columnDefinition = "TEXT")
    String address;

    @Column(name = "password")
    String password;

    @Column(name = "gender")
    String gender;

    @ManyToOne
    @JoinColumn(name = "role_id")
    RoleEntity role;

    @OneToMany(mappedBy = "user")
    List<OrderEntity> orders;

    @OneToMany(mappedBy = "user")
    List<ReviewEntity> reviews;

    @OneToOne(mappedBy = "user")
    CartEntity cart;

    @OneToMany(mappedBy = "user")
    List<WishlistEntity> wishlists;


    public UserEntity (String email, String fullname, String password, RoleEntity roleEntity){
        this.email = email;
        this.fullname = fullname;
        this.password = password;
        this.role = roleEntity;
    }
}
