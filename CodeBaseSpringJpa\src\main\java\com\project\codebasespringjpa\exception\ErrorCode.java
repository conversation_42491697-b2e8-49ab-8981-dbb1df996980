package com.project.codebasespringjpa.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ErrorCode {
    // Common errors
    UNCATEGORIZED_EXCEPTION(-1, "Lỗi không xác định"),
    UNAUTHEN(401, "Chưa đăng nhập"),
    FORBIDDEN(403, "Không có quyền truy cập"),
    RESOURCE_NOT_FOUND(404, "Không tìm thấy tài nguyên"),
    INVALID_REQUEST(400, "<PERSON>êu cầu không hợp lệ"),
    INTERNAL_SERVER_ERROR(500, "Lỗi máy chủ nội bộ"),

    // User errors
    USER_NOT_FOUND(1000, "Không tìm thấy người dùng"),
    USER_ALREADY_EXISTS(1001, "Người dùng đã tồn tại"),
    INVALID_CREDENTIALS(1002, "Thông tin đăng nhập không hợp lệ"),
    USERNAME_ALREADY_EXISTS(1003, "Tên đăng nhập đã tồn tại"),
    EMAIL_ALREADY_EXISTS(1004, "Email đã tồn tại"),
    INCORRECT_PASSWORD(1005, "Mật khẩu không chính xác"),
    ROLE_NOT_FOUND(1006, "Không tìm thấy vai trò"),
    USER_HAS_ORDERS(1007, "Người dùng có đơn hàng, không thể xóa"),
    USER_HAS_REVIEWS(1008, "Người dùng có đánh giá, không thể xóa"),
    USER_HAS_WISHLISTS(1009, "Người dùng có danh sách yêu thích, không thể xóa"),
    USER_HAS_BLOGS(1010, "Người dùng có bài viết, không thể xóa"),
    USER_HAS_CART(1011, "Người dùng có giỏ hàng, không thể xóa"),

    // Category errors
    CATEGORY_NOT_FOUND(2000, "Không tìm thấy danh mục"),
    CATEGORY_ALREADY_EXISTS(2001, "Danh mục đã tồn tại"),
    CATEGORY_NAME_ALREADY_EXISTS(2002, "Tên danh mục đã tồn tại"),
    CATEGORY_SLUG_ALREADY_EXISTS(2003, "Slug danh mục đã tồn tại"),
    CATEGORY_HAS_CHILDREN(2004, "Danh mục có danh mục con"),
    CATEGORY_HAS_PRODUCTS(2005, "Danh mục có sản phẩm"),

    // Product errors
    PRODUCT_NOT_FOUND(3000, "Không tìm thấy sản phẩm"),
    PRODUCT_ALREADY_EXISTS(3001, "Sản phẩm đã tồn tại"),
    PRODUCT_NAME_ALREADY_EXISTS(3002, "Tên sản phẩm đã tồn tại"),
    PRODUCT_SLUG_ALREADY_EXISTS(3003, "Slug sản phẩm đã tồn tại"),
    PRODUCT_SKU_ALREADY_EXISTS(3004, "SKU sản phẩm đã tồn tại"),

    // Product variant errors
    PRODUCT_VARIANT_NOT_FOUND(3100, "Không tìm thấy biến thể sản phẩm"),
    PRODUCT_VARIANT_ALREADY_EXISTS(3101, "Biến thể sản phẩm đã tồn tại"),
    PRODUCT_VARIANT_SKU_ALREADY_EXISTS(3102, "SKU biến thể sản phẩm đã tồn tại"),

    // Supplier errors
    SUPPLIER_NOT_FOUND(4000, "Không tìm thấy nhà cung cấp"),
    SUPPLIER_ALREADY_EXISTS(4001, "Nhà cung cấp đã tồn tại"),
    SUPPLIER_NAME_ALREADY_EXISTS(4002, "Tên nhà cung cấp đã tồn tại"),
    SUPPLIER_EMAIL_ALREADY_EXISTS(4003, "Email nhà cung cấp đã tồn tại"),
    SUPPLIER_HAS_PRODUCTS(4004, "Nhà cung cấp có sản phẩm"),

    // Order errors
    ORDER_NOT_FOUND(5000, "Không tìm thấy đơn hàng"),
    ORDER_ITEM_NOT_FOUND(5001, "Không tìm thấy mục đơn hàng"),
    INVALID_ORDER_STATUS(5002, "Trạng thái đơn hàng không hợp lệ"),

    // Blog errors
    BLOG_NOT_FOUND(6000, "Không tìm thấy bài viết"),
    BLOG_ALREADY_EXISTS(6001, "Bài viết đã tồn tại"),
    BLOG_TITLE_ALREADY_EXISTS(6002, "Tiêu đề bài viết đã tồn tại"),
    BLOG_SLUG_ALREADY_EXISTS(6003, "Slug bài viết đã tồn tại"),

    // Review errors
    REVIEW_NOT_FOUND(7000, "Không tìm thấy đánh giá"),
    REVIEW_ALREADY_EXISTS(7001, "Đánh giá đã tồn tại"),

    // Cart errors
    CART_NOT_FOUND(8000, "Không tìm thấy giỏ hàng"),
    CART_ITEM_NOT_FOUND(8001, "Không tìm thấy mục giỏ hàng"),

    // Wishlist errors
    WISHLIST_NOT_FOUND(9000, "Không tìm thấy danh sách yêu thích"),
    WISHLIST_ALREADY_EXISTS(9001, "Danh sách yêu thích đã tồn tại"),

    // Coupon errors
    COUPON_NOT_FOUND(10000, "Không tìm thấy mã giảm giá"),
    COUPON_ALREADY_EXISTS(10001, "Mã giảm giá đã tồn tại"),
    COUPON_CODE_ALREADY_EXISTS(10002, "Mã giảm giá đã tồn tại"),
    COUPON_EXPIRED(10003, "Mã giảm giá đã hết hạn"),
    COUPON_NOT_ACTIVE(10004, "Mã giảm giá không hoạt động"),
    COUPON_USAGE_LIMIT_EXCEEDED(10005, "Đã vượt quá giới hạn sử dụng mã giảm giá"),
    COUPON_MINIMUM_ORDER_AMOUNT_NOT_MET(10006, "Chưa đạt giá trị đơn hàng tối thiểu"),

    // File errors
    FILE_UPLOAD_ERROR(11000, "Lỗi tải lên tệp"),
    INVALID_FILE_TYPE(11001, "Loại tệp không hợp lệ"),
    FILE_SIZE_EXCEEDED(11002, "Kích thước tệp vượt quá giới hạn");

    private int code;
    private String message;
}
