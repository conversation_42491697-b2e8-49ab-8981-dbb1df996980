package com.project.codebasespringjpa.mapper;

import com.project.codebasespringjpa.dto.wishlist.request.WishlistCreateRequest;
import com.project.codebasespringjpa.dto.wishlist.response.WishlistResponse;
import com.project.codebasespringjpa.entity.ProductEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.entity.WishlistEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class WishlistMapper {
    
    private final ProductMapper productMapper;
    
    /**
     * Chuyển đổi từ WishlistEntity sang WishlistResponse
     */
    public WishlistResponse toResponse(WishlistEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return WishlistResponse.builder()
                .id(entity.getId())
                .userId(entity.getUser() != null ? entity.getUser().getId() : null)
                .userName(entity.getUser() != null ? entity.getUser().getFullname() : null)
                .userEmail(entity.getUser() != null ? entity.getUser().getEmail() : null)
                .product(entity.getProduct() != null ? productMapper.toResponse(entity.getProduct()) : null)
                .addedDate(entity.getAddedDate())
                .createDate(entity.getCreateDate())
                .createBy(entity.getCreateBy())
                .updateDate(entity.getUpdateDate())
                .updateBy(entity.getUpdateBy())
                .build();
    }
    
    /**
     * Chuyển đổi từ WishlistCreateRequest sang WishlistEntity
     */
    public WishlistEntity toEntity(WishlistCreateRequest request, UserEntity user, ProductEntity product) {
        if (request == null) {
            return null;
        }
        
        return WishlistEntity.builder()
                .user(user)
                .product(product)
                .addedDate(LocalDateTime.now())
                .build();
    }
    
    /**
     * Chuyển đổi danh sách WishlistEntity sang danh sách WishlistResponse
     */
    public List<WishlistResponse> toResponseList(List<WishlistEntity> entities) {
        if (entities == null) {
            return new ArrayList<>();
        }
        
        return entities.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }
}
