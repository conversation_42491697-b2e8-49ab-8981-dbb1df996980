package com.project.codebasespringjpa.controller;

import com.project.codebasespringjpa.dto.review.request.ReviewCreateRequest;
import com.project.codebasespringjpa.dto.review.request.ReviewUpdateRequest;
import com.project.codebasespringjpa.dto.review.response.ReviewResponse;
import com.project.codebasespringjpa.service.interfaces.IReviewService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/reviews")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ReviewController {
    IReviewService reviewService;

    @GetMapping
    public ResponseEntity<List<ReviewResponse>> findAll() {
        return ResponseEntity.ok(reviewService.findAll());
    }

    @GetMapping("/{id}")
    public ResponseEntity<ReviewResponse> findById(@PathVariable Long id) {
        return ResponseEntity.ok(reviewService.findById(id));
    }

    @GetMapping("/product/{productId}")
    public ResponseEntity<List<ReviewResponse>> findByProductId(@PathVariable Long productId) {
        return ResponseEntity.ok(reviewService.findByProductId(productId));
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<List<ReviewResponse>> findByUserId(@PathVariable Long userId) {
        return ResponseEntity.ok(reviewService.findByUserId(userId));
    }

    @GetMapping("/status/{status}")
    public ResponseEntity<List<ReviewResponse>> findByStatus(@PathVariable Boolean status) {
        return ResponseEntity.ok(reviewService.findByStatus(status));
    }

    @GetMapping("/product/{productId}/rating")
    public ResponseEntity<Double> getAverageRatingByProduct(@PathVariable Long productId) {
        return ResponseEntity.ok(reviewService.getAverageRatingByProduct(productId));
    }

    @PostMapping
    public ResponseEntity<ReviewResponse> create(@RequestBody ReviewCreateRequest request) {
        return new ResponseEntity<>(reviewService.create(request), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ReviewResponse> update(@PathVariable Long id, @RequestBody ReviewUpdateRequest request) {
        return ResponseEntity.ok(reviewService.update(id, request));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        reviewService.delete(id);
        return ResponseEntity.noContent().build();
    }
}
