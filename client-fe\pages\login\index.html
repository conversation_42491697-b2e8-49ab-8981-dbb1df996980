<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đăng nhập - <PERSON> Shop</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.min.css">
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Main Content -->
    <main class="container my-5">
        <div class="auth-container">
            <h2 class="text-center mb-4">Đăng nhập</h2>
            
            <form id="loginForm">
                <div class="mb-3">
                    <label for="email" class="form-label">Email hoặc tên đăng nhập</label>
                    <input type="text" class="form-control" id="email" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Mật khẩu</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="password" required>
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="far fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="rememberMe">
                    <label class="form-check-label" for="rememberMe">Ghi nhớ đăng nhập</label>
                </div>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">Đăng nhập</button>
                </div>
            </form>
            
            <div class="text-center mt-3">
                <a href="#" id="forgotPasswordLink">Quên mật khẩu?</a>
            </div>
            
            <hr class="my-4">
            
            <div class="text-center">
                <p>Chưa có tài khoản? <a href="../register/index.html">Đăng ký ngay</a></p>
            </div>
            
            <div class="mt-4">
                <p class="text-center mb-3">Hoặc đăng nhập với</p>
                <div class="d-flex justify-content-center gap-3">
                    <button class="btn btn-outline-primary" id="facebookLogin">
                        <i class="fab fa-facebook-f me-2"></i> Facebook
                    </button>
                    <button class="btn btn-outline-danger" id="googleLogin">
                        <i class="fab fa-google me-2"></i> Google
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.5/dist/sweetalert2.all.min.js"></script>
    <!-- Custom JS -->
    <script src="../../assets/js/services/api-config.js"></script>
    <script src="../../assets/js/services/auth-service.js"></script>
    <script src="../../assets/js/common.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle password visibility
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });
            
            // Handle login form submission
            const loginForm = document.getElementById('loginForm');
            
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                const rememberMe = document.getElementById('rememberMe').checked;
                
                try {
                    showLoading();
                    
                    // Call login API (dùng email làm username)
                    const response = await AuthService.login(email, password);
                    
                    if (rememberMe) {
                        localStorage.setItem('rememberMe', 'true');
                    }
                    
                    hideLoading();
                    
                    // Show success message
                    Swal.fire({
                        title: 'Đăng nhập thành công!',
                        text: 'Chào mừng bạn quay trở lại',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        // Redirect based on role
                        const role = localStorage.getItem('role');
                        if (role === 'ADMIN') {
                            window.location.href = '../../../admin-fe/index.html';
                        } else {
                            window.location.href = '../../index.html';
                        }
                    });
                } catch (error) {
                    hideLoading();
                    
                    Swal.fire({
                        title: 'Đăng nhập thất bại',
                        text: error.message || 'Email hoặc mật khẩu không đúng',
                        icon: 'error',
                        confirmButtonText: 'Thử lại'
                    });
                }
            });
            
            // Handle forgot password
            const forgotPasswordLink = document.getElementById('forgotPasswordLink');
            
            forgotPasswordLink.addEventListener('click', function(e) {
                e.preventDefault();
                
                Swal.fire({
                    title: 'Quên mật khẩu',
                    input: 'email',
                    inputLabel: 'Nhập email của bạn để nhận hướng dẫn đặt lại mật khẩu',
                    inputPlaceholder: 'Nhập email của bạn',
                    showCancelButton: true,
                    confirmButtonText: 'Gửi',
                    cancelButtonText: 'Hủy',
                    showLoaderOnConfirm: true,
                    preConfirm: (email) => {
                        if (!email) {
                            Swal.showValidationMessage('Vui lòng nhập email');
                            return false;
                        }
                        
                        // Here you would call your API to send reset password email
                        return new Promise((resolve) => {
                            setTimeout(() => {
                                resolve(email);
                            }, 1000);
                        });
                    },
                    allowOutsideClick: () => !Swal.isLoading()
                }).then((result) => {
                    if (result.isConfirmed) {
                        Swal.fire({
                            title: 'Đã gửi email',
                            text: `Hướng dẫn đặt lại mật khẩu đã được gửi đến ${result.value}`,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            });
            
            // Handle social logins
            document.getElementById('facebookLogin').addEventListener('click', function() {
                // Implement Facebook login
                alert('Chức năng đăng nhập bằng Facebook sẽ được triển khai sau');
            });
            
            document.getElementById('googleLogin').addEventListener('click', function() {
                // Implement Google login
                alert('Chức năng đăng nhập bằng Google sẽ được triển khai sau');
            });
        });
    </script>
</body>
</html>
