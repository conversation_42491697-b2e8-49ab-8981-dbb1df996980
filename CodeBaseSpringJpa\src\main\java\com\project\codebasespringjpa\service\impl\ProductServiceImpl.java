package com.project.codebasespringjpa.service.impl;

import com.project.codebasespringjpa.dto.product.request.ProductRequest;
import com.project.codebasespringjpa.dto.product.request.ProductSearch;
import com.project.codebasespringjpa.dto.product.response.ProductResponse;
import com.project.codebasespringjpa.entity.CategoryEntity;
import com.project.codebasespringjpa.entity.ProductEntity;
import com.project.codebasespringjpa.entity.SupplierEntity;
import com.project.codebasespringjpa.exception.AppException;
import com.project.codebasespringjpa.exception.ErrorCode;
import com.project.codebasespringjpa.mapper.ProductMapper;
import com.project.codebasespringjpa.repository.ICategoryRepository;
import com.project.codebasespringjpa.repository.IProductRepository;
import com.project.codebasespringjpa.repository.IReviewRepository;
import com.project.codebasespringjpa.repository.ISupplierRepository;
import com.project.codebasespringjpa.service.interfaces.IProductService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Transactional
public class ProductServiceImpl implements IProductService {
    IProductRepository productRepository;
    ICategoryRepository categoryRepository;
    ISupplierRepository supplierRepository;
    IReviewRepository reviewRepository;
    ProductMapper productMapper;

    @Override
    public Page<ProductResponse> findAll(ProductSearch productSearch, Pageable pageable) {
        return productRepository.findAll(productSearch.getKeyword(), productSearch.getCategoryName(), productSearch.getSupplierName(),
                productSearch.getCategoryId(), productSearch.getSupplierId(),
                pageable).map(it -> productMapper.toResponse(it));
    }

    @Override
    public ProductResponse findById(Long id) {
        ProductEntity product = productRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.PRODUCT_NOT_FOUND));
        return productMapper.toResponse(product);
    }


    @Override
    @Transactional
    public ProductResponse create(ProductRequest request) {
        if (existsByName(request.getName())) {
            throw new AppException(ErrorCode.PRODUCT_NAME_ALREADY_EXISTS);
        }

        ProductEntity product = productMapper.toEntity(request);
        ProductEntity savedProduct = productRepository.save(product);

        return productMapper.toResponse(savedProduct);
    }

    @Override
    public ProductResponse update(Long id, ProductRequest request) {
        ProductEntity product = productRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.PRODUCT_NOT_FOUND));

        CategoryEntity category = CategoryEntity.builder()
                .id(request.getCategoryId())
                .build();

        SupplierEntity supplier = SupplierEntity.builder()
                .id(request.getSupplierId())
                .build();

        product.setName(request.getName());
        product.setDescription(request.getDescription());
        product.setQuantity(request.getQuantity());
        product.setPrice(request.getPrice());
        product.setImage(request.getImage());
        product.setCategory(category);
        product.setSupplier(supplier);

        return productMapper.toResponse(productRepository.save(product));
    }

    @Override
    public void delete(Long id) {
        ProductEntity product = productRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.PRODUCT_NOT_FOUND));

        productRepository.delete(product);
    }

    @Override
    public boolean existsByName(String name) {
        return productRepository.existsByName(name);
    }
}
