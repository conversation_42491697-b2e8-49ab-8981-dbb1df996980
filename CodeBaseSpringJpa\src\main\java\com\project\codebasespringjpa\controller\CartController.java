package com.project.codebasespringjpa.controller;

import com.project.codebasespringjpa.dto.cart.request.CartRequest;
import com.project.codebasespringjpa.dto.cart.request.QuantityRequest;
import com.project.codebasespringjpa.dto.cart.response.CartResponse;
import com.project.codebasespringjpa.service.interfaces.ICartService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/carts")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CartController {
    ICartService cartService;

    @GetMapping("/user/{userId}")
    public ResponseEntity<List<CartResponse>> findByUserId(@PathVariable Long userId) {
        return ResponseEntity.ok(cartService.findByUserId(userId));
    }

    @PostMapping("/create")
    public ResponseEntity<CartResponse> addItem(@RequestBody CartRequest request) {
        return new ResponseEntity<>(cartService.create( request), HttpStatus.CREATED);
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<Void> updateItem(@PathVariable Long id, @RequestBody QuantityRequest request) {
        cartService.updateItem(id, request.getQuantity());
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<CartResponse> delete(@PathVariable Long id) {
        cartService.delete(id);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/delete-all/{userId}")
    public ResponseEntity<Void> clearCart(@PathVariable Long userId) {
        cartService.deleteAll(userId);
        return ResponseEntity.noContent().build();
    }
}
