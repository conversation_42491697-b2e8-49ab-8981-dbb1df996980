// JavaScript for Home Page

document.addEventListener('DOMContentLoaded', function() {
    // Load layout components
    loadLayoutComponents();

    // Load data
    loadCategories();
    loadProducts();
    loadBlogs();

    // Lắng nghe sự kiện click cho danh mục (event delegation)
    document.getElementById('featured-categories-container').addEventListener('click', function(e) {
        const btn = e.target.closest('.btn-view-category');
        if (btn) {
            const categoryId = btn.getAttribute('data-category-id');
            const categoryName = btn.getAttribute('data-category-name');
            filterProductsByCategory(categoryId, categoryName);
        }
    });

    // Lắng nghe sự kiện click cho nút "Xem tất cả sản phẩm" (nếu có)
    document.getElementById('featured-products-container').addEventListener('click', function(e) {
        const btn = e.target.closest('.btn-view-all-products');
        if (btn) {
            loadProducts();
            document.getElementById('featured-products-title').textContent = 'Sản phẩm nổi bật';
        }
    });
});

// Helper: Chuẩn hóa đường dẫn ảnh
function normalizeImageUrl(url) {
    if (!url) return 'assets/image/default-category.jpg';
    if (url.startsWith('http')) return url;
    // Nếu là đường dẫn tương đối, thêm domain backend
    return 'http://localhost:8080/' + url.replace(/^\/+/, '');
}

// Load layout components
function loadLayoutComponents() {
    // Load header
    fetch('../../layout/header/header.html')
        .then(response => response.text())
        .then(html => {
            document.getElementById('header-container').innerHTML = html;
            // Load header script after HTML is inserted
            const headerScript = document.createElement('script');
            headerScript.src = '../../layout/header/header.js';
            document.body.appendChild(headerScript);
        });

    // Load footer
    fetch('../../layout/footer/footer.html')
        .then(response => response.text())
        .then(html => {
            document.getElementById('footer-container').innerHTML = html;
        });
}

// Load categories
async function loadCategories() {
    try {
        const categories = await apiService.getCategories();
        const container = document.getElementById('featured-categories-container');
        
        // Clear loading spinner
        container.innerHTML = '';
        
        // Display categories
        const categoriesToDisplay = categories.filter(category => category.image || true).slice(0, 3);

        if (categoriesToDisplay.length === 0) {
             container.innerHTML = `
                <div class="col-12 text-center text-muted">
                    <p>Không tìm thấy danh mục.</p>
                </div>
            `;
            return;
        }

        categoriesToDisplay.forEach(category => {
            const categoryHtml = `
                <div class="col-md-4 mb-4">
                    <div class="category-card">
                        <img src="${normalizeImageUrl(category.image)}" alt="${category.name}" class="img-fluid">
                        <div class="category-overlay">
                            <h3>${category.name}</h3>
                            <button class="btn btn-outline-light btn-view-category" data-category-id="${category.id}" data-category-name="${category.name}">Xem thêm</button>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML += categoryHtml;
        });
    } catch (error) {
        console.error('Error loading categories:', error);
        document.getElementById('featured-categories-container').innerHTML = `
            <div class="col-12 text-center text-danger">
                <p>Không thể tải danh mục. Vui lòng thử lại sau.</p>
            </div>
        `;
    }
}

// Load products
async function loadProducts() {
    try {
        const response = await apiService.getProducts(0, 8);
        const products = response.content || response;
        const container = document.getElementById('featured-products-container');
        
        // Clear loading spinner
        container.innerHTML = '';

         if ((Array.isArray(products) && products.length === 0) || (!Array.isArray(products) && !products)) {
             container.innerHTML = `
                <div class="col-12 text-center text-muted">
                    <p>Không tìm thấy sản phẩm.</p>
                </div>
            `;
            return;
        }
        
        // Display products
        products.forEach(product => {
            const productHtml = `
                <div class="col-md-3 mb-4">
                    <div class="card h-100">
                        <img src="${normalizeImageUrl(product.image)}" class="card-img-top" alt="${product.name}">
                        <div class="card-body">
                            <h5 class="card-title">${product.name}</h5>
                            <p class="card-text text-danger fw-bold">${formatCurrency(product.price)}</p>
                            <div class="d-flex justify-content-between">
                                <a href="pages/product/detail.html?id=${product.id}" class="btn btn-outline-primary">Chi tiết</a>
                                <button class="btn btn-primary" onclick="addToCart(${product.id})">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML += productHtml;
        });
        // Hiện lại nút xem tất cả sản phẩm
        document.getElementById('btn-view-all-products').classList.add('d-inline-block');
        document.getElementById('btn-view-all-products').classList.remove('d-none');
    } catch (error) {
        console.error('Error loading products:', error);
        document.getElementById('featured-products-container').innerHTML = `
            <div class="col-12 text-center text-danger">
                <p>Không thể tải sản phẩm. Vui lòng thử lại sau.</p>
            </div>
        `;
    }
}

// Lọc sản phẩm theo danh mục
async function filterProductsByCategory(categoryId, categoryName) {
    try {
        const response = await apiService.getProductsByCategory(categoryId, 1, 8); // page=1, size=8
        const products = response.content || response;
        const container = document.getElementById('featured-products-container');
        container.innerHTML = '';
        document.getElementById('featured-products-title').textContent = `Sản phẩm thuộc danh mục: ${categoryName}`;
        if ((Array.isArray(products) && products.length === 0) || (!Array.isArray(products) && !products)) {
            container.innerHTML = `<div class="col-12 text-center text-muted"><p>Không tìm thấy sản phẩm cho danh mục này.</p></div>`;
            return;
        }
        products.forEach(product => {
            const productHtml = `
                <div class="col-md-3 mb-4">
                    <div class="card h-100">
                        <img src="${normalizeImageUrl(product.image)}" class="card-img-top" alt="${product.name}">
                        <div class="card-body">
                            <h5 class="card-title">${product.name}</h5>
                            <p class="card-text text-danger fw-bold">${formatCurrency(product.price)}</p>
                            <div class="d-flex justify-content-between">
                                <a href="pages/product/detail.html?id=${product.id}" class="btn btn-outline-primary">Chi tiết</a>
                                <button class="btn btn-primary" onclick="addToCart(${product.id})">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML += productHtml;
        });
        // Hiện nút xem tất cả sản phẩm
        document.getElementById('btn-view-all-products').classList.remove('d-none');
        document.getElementById('btn-view-all-products').classList.add('d-inline-block');
    } catch (error) {
        console.error('Error filter products by category:', error);
        document.getElementById('featured-products-container').innerHTML = `<div class="col-12 text-center text-danger"><p>Không thể tải sản phẩm. Vui lòng thử lại sau.</p></div>`;
    }
}

// Load blogs
async function loadBlogs() {
    try {
        const response = await apiService.getBlogs(0, 3);
        const blogs = response.content || response;
        const container = document.getElementById('latest-posts-container');
        
        // Clear loading spinner
        container.innerHTML = '';

         if ((Array.isArray(blogs) && blogs.length === 0) || (!Array.isArray(blogs) && !blogs)) {
             container.innerHTML = `
                <div class="col-12 text-center text-muted">
                    <p>Không tìm thấy bài viết.</p>
                </div>
            `;
            return;
        }
        
        // Display blogs
        blogs.forEach(blog => {
            const blogHtml = `
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <img src="${normalizeImageUrl(blog.image)}" class="card-img-top" alt="${blog.title}">
                        <div class="card-body">
                            <h5 class="card-title">${blog.title}</h5>
                            <p class="card-text">${blog.summary || ''}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">${formatDate(blog.createDate)}</small>
                                <a href="pages/blog/detail.html?id=${blog.id}" class="btn btn-outline-primary">Đọc thêm</a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML += blogHtml;
        });
    } catch (error) {
        console.error('Error loading blogs:', error);
        document.getElementById('latest-posts-container').innerHTML = `
            <div class="col-12 text-center text-danger">
                <p>Không thể tải bài viết. Vui lòng thử lại sau.</p>
            </div>
        `;
    }
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
    }).format(amount);
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('vi-VN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}

// Add to cart function
async function addToCart(productId) {
    try {
        console.log('=== THÊM VÀO GIỎ HÀNG ===');
        console.log('Product ID:', productId);

        // Get user ID from localStorage
        const userId = localStorage.getItem('userId');
        console.log('User ID từ localStorage:', userId);

        if (!userId) {
            console.log('❌ Không tìm thấy User ID - chuyển hướng đến đăng nhập');
            Swal.fire({
                icon: 'warning',
                title: 'Vui lòng đăng nhập',
                text: 'Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng',
                confirmButtonText: 'Đăng nhập ngay'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = 'pages/auth/login.html';
                }
            });
            return;
        }

        // Get product details to get price
        console.log('🔍 Lấy thông tin sản phẩm...');
        const product = await apiRequest(`products/${productId}`);
        console.log('Thông tin sản phẩm:', product);

        if (!product) {
            console.log('❌ Không tìm thấy sản phẩm');
            throw new Error('Product not found');
        }

        // Prepare cart data
        const cartData = {
            productId: parseInt(productId),
            quantity: 1, // Default quantity
            price: product.salePrice || product.price,
            userId: parseInt(userId)
        };

        console.log('📦 Dữ liệu gửi lên API:');
        console.log('URL:', 'http://localhost:8080/api/carts/create');
        console.log('Method: POST');
        console.log('Data:', JSON.stringify(cartData, null, 2));

        // Call API to add to cart
        console.log('🚀 Gọi API thêm vào giỏ hàng...');
        const result = await apiRequest('api/carts/create', 'POST', cartData);
        console.log('✅ Kết quả từ API:', result);

        // Update cart count
        updateCartCount();
        console.log('🔄 Đã cập nhật số lượng giỏ hàng');

        // Show success message
        Swal.fire({
            icon: 'success',
            title: 'Thêm vào giỏ hàng thành công!',
            text: `Đã thêm "${product.name}" vào giỏ hàng`,
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
        });

        console.log('=== KẾT THÚC THÊM VÀO GIỎ HÀNG ===');

    } catch (error) {
        console.error('❌ Lỗi khi thêm vào giỏ hàng:', error);
        console.error('Chi tiết lỗi:', error.message);
        console.error('Stack trace:', error.stack);

        Swal.fire({
            icon: 'error',
            title: 'Lỗi!',
            text: 'Không thể thêm sản phẩm vào giỏ hàng. Vui lòng thử lại.',
            confirmButtonText: 'OK'
        });
    }
}