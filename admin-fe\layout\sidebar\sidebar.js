document.addEventListener('DOMContentLoaded', function() {
  // <PERSON><PERSON><PERSON> dấu menu đang active dựa trên URL hiện tại
  const currentLocation = window.location.pathname;
  const navLinks = document.querySelectorAll('.sidebar .nav-link');
  
  navLinks.forEach(link => {
    if (currentLocation.includes(link.getAttribute('href'))) {
      link.classList.add('active');
    } else {
      link.classList.remove('active');
    }
  });
});
