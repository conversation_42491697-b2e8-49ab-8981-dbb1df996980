package com.project.codebasespringjpa.service.impl;

import com.project.codebasespringjpa.dto.supplier.request.SupplierCreateRequest;
import com.project.codebasespringjpa.dto.supplier.request.SupplierUpdateRequest;
import com.project.codebasespringjpa.dto.supplier.response.SupplierResponse;
import com.project.codebasespringjpa.entity.SupplierEntity;
import com.project.codebasespringjpa.exception.AppException;
import com.project.codebasespringjpa.exception.ErrorCode;
import com.project.codebasespringjpa.mapper.SupplierMapper;
import com.project.codebasespringjpa.repository.ISupplierRepository;
import com.project.codebasespringjpa.service.interfaces.ISupplierService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SupplierServiceImpl implements ISupplierService {
    ISupplierRepository supplierRepository;
    SupplierMapper supplierMapper;

    @Override
    public List<SupplierResponse> findAll(String keyword) {
        List<SupplierEntity> suppliers = supplierRepository.findAll(keyword);
        return supplierMapper.toResponseList(suppliers);
    }

    @Override
    public SupplierResponse findById(Long id) {
        SupplierEntity supplier = supplierRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.SUPPLIER_NOT_FOUND));
        return supplierMapper.toResponse(supplier);
    }

    @Override
    public SupplierResponse findByName(String name) {
        SupplierEntity supplier = supplierRepository.findByName(name)
                .orElseThrow(() -> new AppException(ErrorCode.SUPPLIER_NOT_FOUND));
        return supplierMapper.toResponse(supplier);
    }

    @Override
    public SupplierResponse findByEmail(String email) {
        SupplierEntity supplier = supplierRepository.findByEmail(email)
                .orElseThrow(() -> new AppException(ErrorCode.SUPPLIER_NOT_FOUND));
        return supplierMapper.toResponse(supplier);
    }

    @Override
    public SupplierResponse create(SupplierCreateRequest request) {
        if (existsByName(request.getName())) {
            throw new AppException(ErrorCode.SUPPLIER_NAME_ALREADY_EXISTS);
        }

        if (request.getEmail() != null && existsByEmail(request.getEmail())) {
            throw new AppException(ErrorCode.SUPPLIER_EMAIL_ALREADY_EXISTS);
        }

        SupplierEntity supplier = supplierMapper.toEntity(request);
        SupplierEntity savedSupplier = supplierRepository.save(supplier);
        return supplierMapper.toResponse(savedSupplier);
    }

    @Override
    public SupplierResponse update(Long id, SupplierUpdateRequest request) {
        SupplierEntity supplier = supplierRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.SUPPLIER_NOT_FOUND));

        if (request.getName() != null && !request.getName().equals(supplier.getName()) && existsByName(request.getName())) {
            throw new AppException(ErrorCode.SUPPLIER_NAME_ALREADY_EXISTS);
        }

        if (request.getEmail() != null && !request.getEmail().equals(supplier.getEmail()) && existsByEmail(request.getEmail())) {
            throw new AppException(ErrorCode.SUPPLIER_EMAIL_ALREADY_EXISTS);
        }

        supplierMapper.updateEntityFromRequest(supplier, request);
        SupplierEntity updatedSupplier = supplierRepository.save(supplier);
        return supplierMapper.toResponse(updatedSupplier);
    }

    @Override
    public void delete(Long id) {
        SupplierEntity supplier = supplierRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.SUPPLIER_NOT_FOUND));

        // Kiểm tra xem có sản phẩm không
        if (supplier.getProducts() != null && !supplier.getProducts().isEmpty()) {
            throw new AppException(ErrorCode.SUPPLIER_HAS_PRODUCTS);
        }

        supplierRepository.delete(supplier);
    }

    @Override
    public boolean existsByName(String name) {
        return supplierRepository.existsByName(name);
    }

    @Override
    public boolean existsByEmail(String email) {
        return supplierRepository.existsByEmail(email);
    }
}
