package com.project.codebasespringjpa.service.impl;

import com.project.codebasespringjpa.dto.cart.request.CartRequest;
import com.project.codebasespringjpa.dto.cart.response.CartResponse;
import com.project.codebasespringjpa.entity.CartEntity;
import com.project.codebasespringjpa.exception.AppException;
import com.project.codebasespringjpa.exception.ErrorCode;
import com.project.codebasespringjpa.mapper.CartMapper;
import com.project.codebasespringjpa.repository.ICartRepository;
import com.project.codebasespringjpa.service.interfaces.ICartService;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
@Transactional
public class CartServiceImpl implements ICartService {
    @Autowired
    ICartRepository cartRepository;

    @Autowired
    CartMapper cartMapper;

    @Override
    public List<CartResponse> findByUserId(Long userId) {
        return cartRepository.findByUserId(userId).stream().map(it -> cartMapper.toResponse(it)).toList();
    }

    @Override
    public CartResponse create(CartRequest request) {
        return cartMapper.toResponse(cartRepository.save(cartMapper.toEntity(request)));
    }

    @Override
    public void updateItem(Long cartId, Integer quantity) {
        CartEntity cart = cartRepository.findById(cartId).orElseThrow(
                () -> new AppException(ErrorCode.CART_NOT_FOUND)
        );

        cart.setQuantity(quantity);
        cartRepository.save(cart);
    }

    @Override
    public void delete(Long cartId) {
        CartEntity cart = cartRepository.findById(cartId).orElseThrow(
                () -> new AppException(ErrorCode.CART_NOT_FOUND)
        );

        cartRepository.delete(cart);
    }

    @Override
    public void deleteAll(Long userId) {
        List<CartEntity> cartEntities = cartRepository.findByUserId(userId);
        for (var it: cartEntities)
            cartRepository.delete(it);
    }
}
