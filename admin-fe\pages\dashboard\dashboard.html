<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard - Tina Shop Admin</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.3/dist/sweetalert2.min.css">
  <!-- Chart.js -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.0.0/dist/chart.min.css">
  <!-- Common CSS -->
  <link rel="stylesheet" href="../../assets/css/common.css">
  <!-- Layout CSS -->
  <link rel="stylesheet" href="../../layout/header/header.css">
  <link rel="stylesheet" href="../../layout/sidebar/sidebar.css">
  <link rel="stylesheet" href="../../layout/footer/footer.css">
  <!-- Page CSS -->
  <link rel="stylesheet" href="dashboard.css">
</head>
<body>
  <!-- Header -->
  <div id="header-container"></div>

  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <div id="sidebar-container"></div>

      <!-- Main content -->
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
          <h1 class="h2">Dashboard</h1>
          <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
              <button type="button" class="btn btn-sm btn-outline-secondary">Xuất báo cáo</button>
              <button type="button" class="btn btn-sm btn-outline-secondary">Xuất Excel</button>
            </div>
            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle">
              <i class="bi bi-calendar"></i>
              Tuần này
            </button>
          </div>
        </div>

        <!-- Thống kê tổng quan -->
        <div class="row">
          <div class="col-md-3 mb-4">
            <div class="card text-white bg-primary">
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <h6 class="card-title">Tổng đơn hàng</h6>
                    <h2 class="card-text" id="totalOrders">0</h2>
                  </div>
                  <i class="bi bi-cart fs-1"></i>
                </div>
                <p class="card-text mt-2"><small>Tăng 5% so với tháng trước</small></p>
              </div>
            </div>
          </div>

          <div class="col-md-3 mb-4">
            <div class="card text-white bg-success">
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <h6 class="card-title">Doanh thu</h6>
                    <h2 class="card-text" id="totalRevenue">0 ₫</h2>
                  </div>
                  <i class="bi bi-currency-dollar fs-1"></i>
                </div>
                <p class="card-text mt-2"><small>Tăng 12% so với tháng trước</small></p>
              </div>
            </div>
          </div>

          <div class="col-md-3 mb-4">
            <div class="card text-white bg-info">
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <h6 class="card-title">Tổng sản phẩm</h6>
                    <h2 class="card-text" id="totalProducts">0</h2>
                  </div>
                  <i class="bi bi-box fs-1"></i>
                </div>
                <p class="card-text mt-2"><small>Tăng 8% so với tháng trước</small></p>
              </div>
            </div>
          </div>

          <div class="col-md-3 mb-4">
            <div class="card text-white bg-warning">
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <h6 class="card-title">Tổng khách hàng</h6>
                    <h2 class="card-text" id="totalCustomers">0</h2>
                  </div>
                  <i class="bi bi-people fs-1"></i>
                </div>
                <p class="card-text mt-2"><small>Tăng 3% so với tháng trước</small></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Biểu đồ doanh thu -->
        <div class="row">
          <div class="col-md-8 mb-4">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Doanh thu theo tháng</h5>
              </div>
              <div class="card-body">
                <canvas id="revenueChart"></canvas>
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-4">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Đơn hàng theo trạng thái</h5>
              </div>
              <div class="card-body">
                <canvas id="orderStatusChart"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Đơn hàng gần đây -->
        <div class="row">
          <div class="col-md-12 mb-4">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title">Đơn hàng gần đây</h5>
                <a href="/pages/orders/orders.html" class="btn btn-sm btn-primary">Xem tất cả</a>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped table-hover">
                    <thead>
                      <tr>
                        <th>Mã đơn</th>
                        <th>Khách hàng</th>
                        <th>Ngày đặt</th>
                        <th>Tổng tiền</th>
                        <th>Trạng thái</th>
                        <th>Hành động</th>
                      </tr>
                    </thead>
                    <tbody id="recentOrdersTable">
                      <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sản phẩm bán chạy -->
        <div class="row">
          <div class="col-md-6 mb-4">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title">Sản phẩm bán chạy</h5>
                <a href="/pages/products/products.html" class="btn btn-sm btn-primary">Xem tất cả</a>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped table-hover">
                    <thead>
                      <tr>
                        <th>Sản phẩm</th>
                        <th>Danh mục</th>
                        <th>Giá</th>
                        <th>Đã bán</th>
                      </tr>
                    </thead>
                    <tbody id="topProductsTable">
                      <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- Khách hàng mới -->
          <div class="col-md-6 mb-4">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title">Khách hàng mới</h5>
                <a href="/pages/users/users.html" class="btn btn-sm btn-primary">Xem tất cả</a>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped table-hover">
                    <thead>
                      <tr>
                        <th>Khách hàng</th>
                        <th>Email</th>
                        <th>Ngày đăng ký</th>
                        <th>Đơn hàng</th>
                      </tr>
                    </thead>
                    <tbody id="newCustomersTable">
                      <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Footer -->
  <div id="footer-container"></div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <!-- Axios -->
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.3/dist/sweetalert2.all.min.js"></script>
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
  <!-- Common JS -->
  <script src="../../assets/js/common.js"></script>
  <!-- Layout JS -->
  <script src="../../layout/header/header.js"></script>
  <script src="../../layout/sidebar/sidebar.js"></script>
  <!-- Page JS -->
  <script src="dashboard.js"></script>
</body>
</html>
