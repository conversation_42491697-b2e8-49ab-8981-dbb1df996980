package com.project.codebasespringjpa.mapper;

import com.project.codebasespringjpa.dto.cart.request.CartRequest;
import com.project.codebasespringjpa.dto.cart.response.CartResponse;
import com.project.codebasespringjpa.dto.product.response.ProductResponse;
import com.project.codebasespringjpa.entity.CartEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.service.interfaces.IProductService;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CartMapper {
    @Autowired
    IProductService productService;

    public CartEntity toEntity(CartRequest request){
        UserEntity user = UserEntity.builder()
                .id(request.getUserId())
                .build();

        return CartEntity.builder()
                .productId(request.getProductId())
                .quantity(request.getQuantity())
                .price(request.getPrice())
                .user(user)
                .build();
    }

    public CartResponse toResponse(CartEntity entity){
        ProductResponse product = productService.findById(entity.getProductId());

        return CartResponse.builder()
                .id(entity.getId())

                .productId(entity.getProductId())
                .quantity(entity.getQuantity())
                .price(entity.getPrice())

                .productName(product.getName())
                .image(product.getImage())

                .userId(entity.getUser().getId())
                .username(entity.getUser().getUsername())
                .build();
    }

}
