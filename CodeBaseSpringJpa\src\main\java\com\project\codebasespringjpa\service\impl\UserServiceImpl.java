package com.project.codebasespringjpa.service.impl;

import com.project.codebasespringjpa.configuration.security.UserDetailsImpl;
import com.project.codebasespringjpa.dto.user.request.ChangePasswordRequest;
import com.project.codebasespringjpa.dto.user.request.ChangeRoleRequest;
import com.project.codebasespringjpa.dto.user.request.UserCreateRequest;
import com.project.codebasespringjpa.dto.user.request.UserUpdateRequest;
import com.project.codebasespringjpa.dto.user.response.UserResponse;
import com.project.codebasespringjpa.entity.RoleEntity;
import com.project.codebasespringjpa.entity.UserEntity;
import com.project.codebasespringjpa.exception.AppException;
import com.project.codebasespringjpa.exception.ErrorCode;
import com.project.codebasespringjpa.mapper.UserMapper;
import com.project.codebasespringjpa.repository.IRoleRepository;
import com.project.codebasespringjpa.repository.IUserRepository;
import com.project.codebasespringjpa.service.interfaces.IUserService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserServiceImpl implements IUserService {
    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);
    IUserRepository userRepository;
    IRoleRepository roleRepository;
    UserMapper userMapper;
    PasswordEncoder passwordEncoder;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        UserEntity user = userRepository.findByUsernameAndIsDeleteFalse(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with username: " + username));

        return UserDetailsImpl.build(user);
    }

    @Override
    public long count() {
        return userRepository.count();
    }

    @Override
    public UserDetailsImpl getUserInContext() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !authentication.isAuthenticated() ||
                "anonymousUser".equals(authentication.getPrincipal())) {
            throw new AppException(ErrorCode.UNAUTHEN);
        }

        return (UserDetailsImpl) authentication.getPrincipal();
    }

    @Override
    public List<UserResponse> findAll() {
        List<UserEntity> users = userRepository.findByIsDeleteFalse();
        return userMapper.toResponseList(users);
    }

    @Override
    public Page<UserResponse> findAll(Pageable pageable) {
        // Sử dụng Pageable mặc định nếu không có sort được chỉ định
        Pageable safePageable = pageable;
        if (pageable.getSort().isSorted()) {
            // Nếu có sort, tạo một Pageable mới không có sort để tránh lỗi
            safePageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize());
        }
        // Đảm bảo mặc định page=0, limit=10
        if (safePageable.getPageNumber() < 0 || safePageable.getPageSize() <= 0) {
            safePageable = PageRequest.of(0, 10);
        }
        Page<UserEntity> users = userRepository.findByIsDeleteFalse(safePageable);
        return userMapper.toResponsePage(users, safePageable);
    }

    @Override
    public UserResponse findById(Long id) {
        UserEntity user = userRepository.findByIdAndIsDeleteFalse(id)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));
        return userMapper.toResponse(user);
    }

    @Override
    public UserResponse findByUsername(String username) {
        UserEntity user = userRepository.findByUsernameAndIsDeleteFalse(username)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));
        return userMapper.toResponse(user);
    }

    @Override
    public UserResponse findByEmail(String email) {
        UserEntity user = userRepository.findByEmailAndIsDeleteFalse(email)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));
        return userMapper.toResponse(user);
    }

    @Override
    public Page<UserResponse> search(String keyword, Pageable pageable) {
        // Sử dụng Pageable mặc định nếu không có sort được chỉ định
        Pageable safePageable = pageable;
        if (pageable.getSort().isSorted()) {
            // Nếu có sort, tạo một Pageable mới không có sort để tránh lỗi
            safePageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize());
        }
        // Đảm bảo mặc định page=0, limit=10
        if (safePageable.getPageNumber() < 0 || safePageable.getPageSize() <= 0) {
            safePageable = PageRequest.of(0, 10);
        }
        Page<UserEntity> users = userRepository.searchActiveUsers(keyword, safePageable);
        return userMapper.toResponsePage(users, safePageable);
    }

    @Override
    @Transactional
    public UserResponse create(UserCreateRequest request) {
        // Kiểm tra username và email đã tồn tại chưa
        if (existsByUsername(request.getUsername())) {
            throw new AppException(ErrorCode.USERNAME_ALREADY_EXISTS);
        }

        if (existsByEmail(request.getEmail())) {
            throw new AppException(ErrorCode.EMAIL_ALREADY_EXISTS);
        }

        // Lấy role từ request
        RoleEntity role = roleRepository.findByName(request.getRoleName())
                .orElseThrow(() -> new AppException(ErrorCode.ROLE_NOT_FOUND));

        // Tạo user mới
        UserEntity user = userMapper.toEntity(request, role);
        UserEntity savedUser = userRepository.save(user);

        return userMapper.toResponse(savedUser);
    }

    @Override
    @Transactional
    public UserResponse update(Long id, UserUpdateRequest request) {
        UserEntity user = userRepository.findByIdAndIsDeleteFalse(id)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));

        // Kiểm tra username và email đã tồn tại chưa (nếu có thay đổi)
        if (request.getUsername() != null && !request.getUsername().equals(user.getUsername())
                && existsByUsername(request.getUsername())) {
            throw new AppException(ErrorCode.USERNAME_ALREADY_EXISTS);
        }

        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())
                && existsByEmail(request.getEmail())) {
            throw new AppException(ErrorCode.EMAIL_ALREADY_EXISTS);
        }

        // Cập nhật thông tin user
        userMapper.updateEntityFromRequest(user, request);
        UserEntity updatedUser = userRepository.save(user);

        return userMapper.toResponse(updatedUser);
    }

    @Override
    @Transactional
    public void delete(Long id) {
        UserEntity user = userRepository.findByIdAndIsDeleteFalse(id)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));

        // Kiểm tra xem user có liên kết với các entity khác không
        if (!user.getOrders().isEmpty()) {
            throw new AppException(ErrorCode.USER_HAS_ORDERS);
        }

        if (!user.getReviews().isEmpty()) {
            throw new AppException(ErrorCode.USER_HAS_REVIEWS);
        }

        if (!user.getWishlists().isEmpty()) {
            throw new AppException(ErrorCode.USER_HAS_WISHLISTS);
        }

        if (user.getCart() != null) {
            throw new AppException(ErrorCode.USER_HAS_CART);
        }

        // Thực hiện xóa mềm
        user.setIsDelete(true);
        userRepository.save(user);
    }

    @Override
    @Transactional
    public UserResponse changePassword(Long id, ChangePasswordRequest request) {
        UserEntity user = userRepository.findByIdAndIsDeleteFalse(id)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));

        // Kiểm tra mật khẩu cũ
        if (!passwordEncoder.matches(request.getOldPassword(), user.getPassword())) {
            throw new AppException(ErrorCode.INCORRECT_PASSWORD);
        }

        // Cập nhật mật khẩu mới
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        UserEntity updatedUser = userRepository.save(user);

        return userMapper.toResponse(updatedUser);
    }

    @Override
    @Transactional
    public UserResponse changeRole(Long id, ChangeRoleRequest request) {
        UserEntity user = userRepository.findByIdAndIsDeleteFalse(id)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));

        // Lấy role từ request
        RoleEntity role = roleRepository.findByName(request.getRoleName())
                .orElseThrow(() -> new AppException(ErrorCode.ROLE_NOT_FOUND));

        // Cập nhật role
        user.setRole(role);
        UserEntity updatedUser = userRepository.save(user);

        return userMapper.toResponse(updatedUser);
    }

    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }
}
