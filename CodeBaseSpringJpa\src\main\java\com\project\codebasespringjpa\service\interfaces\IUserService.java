package com.project.codebasespringjpa.service.interfaces;

import com.project.codebasespringjpa.configuration.security.UserDetailsImpl;
import com.project.codebasespringjpa.dto.user.request.ChangePasswordRequest;
import com.project.codebasespringjpa.dto.user.request.ChangeRoleRequest;
import com.project.codebasespringjpa.dto.user.request.UserCreateRequest;
import com.project.codebasespringjpa.dto.user.request.UserUpdateRequest;
import com.project.codebasespringjpa.dto.user.response.UserResponse;
import com.project.codebasespringjpa.entity.UserEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.util.List;

public interface IUserService extends UserDetailsService {
    long count();
    UserDetailsImpl getUserInContext();

    List<UserResponse> findAll();
    Page<UserResponse> findAll(Pageable pageable);
    UserResponse findById(Long id);
    UserResponse findByUsername(String username);
    UserResponse findByEmail(String email);
    Page<UserResponse> search(String keyword, Pageable pageable);
    UserResponse create(UserCreateRequest request);
    UserResponse update(Long id, UserUpdateRequest request);
    void delete(Long id);
    UserResponse changePassword(Long id, ChangePasswordRequest request);
    UserResponse changeRole(Long id, ChangeRoleRequest request);
    boolean existsByUsername(String username);
    boolean existsByEmail(String email);
}
