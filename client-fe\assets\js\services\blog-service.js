// Blog Service for Client

// Blog API
const BlogService = {
    getAll: async (page = 0, size = 10) => {
        return await apiRequest(`blogs?page=${page}&size=${size}`);
    },
    
    getById: async (id) => {
        return await apiRequest(`blogs/${id}`);
    },
    
    getBySlug: async (slug) => {
        return await apiRequest(`blogs/slug/${slug}`);
    },
    
    search: async (keyword, page = 0, size = 10) => {
        return await apiRequest(`blogs/search?keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`);
    },
    
    getTopViewed: async (limit = 5) => {
        return await apiRequest(`blogs/top-viewed?limit=${limit}`);
    },
    
    incrementViewCount: async (id) => {
        return await apiRequest(`blogs/${id}/view`, 'PUT');
    }
};
