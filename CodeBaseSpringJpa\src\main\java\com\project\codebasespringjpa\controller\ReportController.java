package com.project.codebasespringjpa.controller;

import com.project.codebasespringjpa.dto.report.InventoryReportResponse;
import com.project.codebasespringjpa.dto.report.SalesReportResponse;
import com.project.codebasespringjpa.service.interfaces.IReportService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping("/api/reports")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ReportController {
    IReportService reportService;

    @GetMapping("/sales")
    public ResponseEntity<SalesReportResponse> getSalesReport(
            @RequestParam(required = false, defaultValue = "month") String period,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFrom,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateTo) {
        
        return ResponseEntity.ok(reportService.getSalesReport(period, dateFrom, dateTo));
    }

    @GetMapping("/inventory")
    public ResponseEntity<InventoryReportResponse> getInventoryReport(
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Long supplierId,
            @RequestParam(required = false) String stockStatus,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        return ResponseEntity.ok(reportService.getInventoryReport(categoryId, supplierId, stockStatus, page, size));
    }

    @GetMapping("/sales/export")
    public ResponseEntity<byte[]> exportSalesReport(
            @RequestParam(required = false, defaultValue = "month") String period,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFrom,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateTo,
            @RequestParam(defaultValue = "pdf") String type) {
        
        byte[] reportBytes = reportService.exportSalesReport(period, dateFrom, dateTo, type);
        
        String filename = "sales_report." + type.toLowerCase();
        return ResponseEntity.ok()
                .header("Content-Disposition", "attachment; filename=" + filename)
                .body(reportBytes);
    }

    @GetMapping("/inventory/export")
    public ResponseEntity<byte[]> exportInventoryReport(
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Long supplierId,
            @RequestParam(required = false) String stockStatus,
            @RequestParam(defaultValue = "pdf") String type) {
        
        byte[] reportBytes = reportService.exportInventoryReport(categoryId, supplierId, stockStatus, type);
        
        String filename = "inventory_report." + type.toLowerCase();
        return ResponseEntity.ok()
                .header("Content-Disposition", "attachment; filename=" + filename)
                .body(reportBytes);
    }
}
