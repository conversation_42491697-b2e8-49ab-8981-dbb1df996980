package com.project.codebasespringjpa.service.impl;

import com.project.codebasespringjpa.dto.product.response.ProductResponse;
import com.project.codebasespringjpa.dto.report.InventoryReportResponse;
import com.project.codebasespringjpa.dto.report.SalesReportResponse;
import com.project.codebasespringjpa.entity.CategoryEntity;
import com.project.codebasespringjpa.entity.OrderEntity;
import com.project.codebasespringjpa.entity.ProductEntity;
import com.project.codebasespringjpa.mapper.ProductMapper;
import com.project.codebasespringjpa.repository.ICategoryRepository;
import com.project.codebasespringjpa.repository.IOrderRepository;
import com.project.codebasespringjpa.repository.IProductRepository;
import com.project.codebasespringjpa.service.interfaces.IReportService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ReportServiceImpl implements IReportService {
    IOrderRepository orderRepository;
    IProductRepository productRepository;
    ICategoryRepository categoryRepository;
    ProductMapper productMapper;

    @Override
    public SalesReportResponse getSalesReport(String period, LocalDate dateFrom, LocalDate dateTo) {
        // Xác định khoảng thời gian
        LocalDateTime startDate, endDate;
        
        if ("custom".equals(period) && dateFrom != null && dateTo != null) {
            startDate = dateFrom.atStartOfDay();
            endDate = dateTo.plusDays(1).atStartOfDay();
        } else {
            // Mặc định là tháng hiện tại
            LocalDate now = LocalDate.now();
            
            switch (period) {
                case "day":
                    startDate = now.atStartOfDay();
                    endDate = now.plusDays(1).atStartOfDay();
                    break;
                case "week":
                    startDate = now.minusDays(now.getDayOfWeek().getValue() - 1).atStartOfDay();
                    endDate = startDate.plusDays(7);
                    break;
                case "quarter":
                    int quarterMonth = ((now.getMonthValue() - 1) / 3) * 3 + 1;
                    startDate = LocalDate.of(now.getYear(), quarterMonth, 1).atStartOfDay();
                    endDate = startDate.plusMonths(3);
                    break;
                case "year":
                    startDate = LocalDate.of(now.getYear(), 1, 1).atStartOfDay();
                    endDate = LocalDate.of(now.getYear() + 1, 1, 1).atStartOfDay();
                    break;
                default: // month
                    startDate = LocalDate.of(now.getYear(), now.getMonth(), 1).atStartOfDay();
                    endDate = startDate.plusMonths(1);
                    break;
            }
        }
        
        // Lấy danh sách đơn hàng trong khoảng thời gian
//        List<OrderEntity> orders = orderRepository.findByOrderDateBetween(startDate, endDate);
        List<OrderEntity> orders = new ArrayList<>();
        
        // Lọc chỉ lấy đơn hàng đã giao
        List<OrderEntity> completedOrders = orders.stream()
                .filter(order -> "DELIVERED".equals(order.getStatus()))
                .collect(Collectors.toList());
        
        // Tính tổng quan
        SalesReportResponse.SalesOverview overview = calculateSalesOverview(completedOrders);
        
        // Tính doanh thu theo ngày
        List<SalesReportResponse.SalesByDate> revenueByDate = calculateRevenueByDate(completedOrders);
        
        // Tính doanh thu theo danh mục
        List<SalesReportResponse.SalesByCategory> revenueByCategory = calculateRevenueByCategory(completedOrders);
        
        // Tính doanh thu theo phương thức thanh toán
        List<SalesReportResponse.SalesByPaymentMethod> revenueByPaymentMethod = calculateRevenueByPaymentMethod(completedOrders);
        
        return SalesReportResponse.builder()
                .overview(overview)
                .revenueByDate(revenueByDate)
                .revenueByCategory(revenueByCategory)
                .revenueByPaymentMethod(revenueByPaymentMethod)
                .build();
    }

    @Override
    public InventoryReportResponse getInventoryReport(Long categoryId, Long supplierId, String stockStatus, int page, int size) {
        // Tạo pageable
        Pageable pageable = PageRequest.of(page, size);
        
        // Lấy danh sách sản phẩm theo bộ lọc
        Page<ProductEntity> productPage;
        
        if (categoryId != null && supplierId != null) {
            productPage = productRepository.findByCategoryIdAndSupplierId(categoryId, supplierId, pageable);
        } else if (categoryId != null) {
            productPage = productRepository.findByCategoryId(categoryId, pageable);
        } else if (supplierId != null) {
            productPage = productRepository.findBySupplierId(supplierId, pageable);
        } else {
            productPage = productRepository.findAll(pageable);
        }
        
        // Lọc theo trạng thái tồn kho nếu có
        List<ProductEntity> allProducts = productRepository.findAll();
        List<ProductEntity> filteredProducts = allProducts;
        
        if (stockStatus != null) {

        }
        
        // Tính tổng quan
        InventoryReportResponse.InventoryOverview overview = calculateInventoryOverview(allProducts);
        
        // Tính tồn kho theo danh mục
        List<InventoryReportResponse.StockByCategory> stockByCategory = calculateStockByCategory(allProducts);
        
        // Tính giá trị tồn kho theo danh mục
        List<InventoryReportResponse.ValueByCategory> valueByCategory = calculateValueByCategory(allProducts);
        
        // Map sang DTO
        Page<ProductResponse> productResponsePage = productPage.map(productMapper::toResponse);
        
        return InventoryReportResponse.builder()
                .overview(overview)
                .stockByCategory(stockByCategory)
                .valueByCategory(valueByCategory)
                .products(productResponsePage)
                .build();
    }

    @Override
    public byte[] exportSalesReport(String period, LocalDate dateFrom, LocalDate dateTo, String type) {
        // Trong thực tế, đây sẽ là code để tạo file PDF hoặc Excel
        // Ở đây chúng ta sẽ trả về một mảng byte giả
        return "Sales Report Export".getBytes();
    }

    @Override
    public byte[] exportInventoryReport(Long categoryId, Long supplierId, String stockStatus, String type) {
        // Trong thực tế, đây sẽ là code để tạo file PDF hoặc Excel
        // Ở đây chúng ta sẽ trả về một mảng byte giả
        return "Inventory Report Export".getBytes();
    }

    private SalesReportResponse.SalesOverview calculateSalesOverview(List<OrderEntity> orders) {

        

        
        return SalesReportResponse.SalesOverview.builder()

                .build();
    }

    private List<SalesReportResponse.SalesByDate> calculateRevenueByDate(List<OrderEntity> orders) {
//        Map<LocalDate, List<OrderEntity>> ordersByDate = orders.stream()
//                .collect(Collectors.groupingBy(order -> order.getOrderDate().toLocalDate()));
        
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
//
//        return ordersByDate.entrySet().stream()
//                .map(entry -> {
//                    LocalDate date = entry.getKey();
//                    List<OrderEntity> dateOrders = entry.getValue();
//
//
//                    int orderCount = dateOrders.size();
//
//
//
//                    return SalesReportResponse.SalesByDate.builder()
//
//                            .build();
//                })
//                .sorted(Comparator.comparing(item -> LocalDate.parse(item.getDate(), formatter)))
//                .collect(Collectors.toList());
        return null;
    }

    private List<SalesReportResponse.SalesByCategory> calculateRevenueByCategory(List<OrderEntity> orders) {
        Map<Long, SalesReportResponse.SalesByCategory> categoryRevenueMap = new HashMap<>();
        

        return new ArrayList<>(categoryRevenueMap.values());
    }

    private List<SalesReportResponse.SalesByPaymentMethod> calculateRevenueByPaymentMethod(List<OrderEntity> orders) {
        Map<String, SalesReportResponse.SalesByPaymentMethod> paymentMethodRevenueMap = new HashMap<>();
        
        orders.forEach(order -> {
            String paymentMethod = order.getPaymentMethod();

            paymentMethodRevenueMap.computeIfAbsent(paymentMethod, k -> 
                SalesReportResponse.SalesByPaymentMethod.builder()
                        .paymentMethod(paymentMethod)
                        .revenue(BigDecimal.ZERO)
                        .orderCount(0)
                        .build()
            );
            
            SalesReportResponse.SalesByPaymentMethod methodRevenue = paymentMethodRevenueMap.get(paymentMethod);
            methodRevenue.setOrderCount(methodRevenue.getOrderCount() + 1);
        });
        
        return new ArrayList<>(paymentMethodRevenueMap.values());
    }

    private InventoryReportResponse.InventoryOverview calculateInventoryOverview(List<ProductEntity> products) {
        int totalProducts = products.size();
        


        
        return InventoryReportResponse.InventoryOverview.builder()

                .build();
    }

    private List<InventoryReportResponse.StockByCategory> calculateStockByCategory(List<ProductEntity> products) {
        Map<Long, InventoryReportResponse.StockByCategory> categoryStockMap = new HashMap<>();
        
        products.forEach(product -> {
            CategoryEntity category = product.getCategory();
            
            if (category != null) {
                Long categoryId = category.getId();
                String categoryName = category.getName();
                
                categoryStockMap.computeIfAbsent(categoryId, k -> 
                    InventoryReportResponse.StockByCategory.builder()
                            .categoryName(categoryName)
                            .stock(0)
                            .build()
                );
                
                InventoryReportResponse.StockByCategory categoryStock = categoryStockMap.get(categoryId);
             }
        });
        
        return new ArrayList<>(categoryStockMap.values());
    }

    private List<InventoryReportResponse.ValueByCategory> calculateValueByCategory(List<ProductEntity> products) {
        Map<Long, InventoryReportResponse.ValueByCategory> categoryValueMap = new HashMap<>();
        
        products.forEach(product -> {
            CategoryEntity category = product.getCategory();
            
            if (category != null) {
                Long categoryId = category.getId();
                String categoryName = category.getName();

                categoryValueMap.computeIfAbsent(categoryId, k -> 
                    InventoryReportResponse.ValueByCategory.builder()
                            .categoryName(categoryName)
                            .value(BigDecimal.ZERO)
                            .build()
                );
                
                InventoryReportResponse.ValueByCategory categoryValue = categoryValueMap.get(categoryId);
            }
        });
        
        return new ArrayList<>(categoryValueMap.values());
    }
}
