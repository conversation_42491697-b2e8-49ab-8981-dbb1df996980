<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>B<PERSON>o c<PERSON>o doanh thu - <PERSON> Shop Admin</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.3/dist/sweetalert2.min.css">
  <!-- Chart.js -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css">
  <!-- Common CSS -->
  <link rel="stylesheet" href="../../assets/css/common.css">
  <!-- Layout CSS -->
  <link rel="stylesheet" href="../../layout/header/header.css">
  <link rel="stylesheet" href="../../layout/sidebar/sidebar.css">
  <link rel="stylesheet" href="../../layout/footer/footer.css">
  <!-- Page CSS -->
  <link rel="stylesheet" href="reports.css">
</head>
<body>
  <!-- Header -->
  <div id="header-container"></div>
  
  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <div id="sidebar-container"></div>
      
      <!-- Main content -->
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
          <h1 class="h2">Báo cáo doanh thu</h1>
          <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
              <button type="button" class="btn btn-sm btn-outline-secondary" id="btnExportPDF">
                <i class="bi bi-file-pdf"></i> Xuất PDF
              </button>
              <button type="button" class="btn btn-sm btn-outline-secondary" id="btnExportExcel">
                <i class="bi bi-file-excel"></i> Xuất Excel
              </button>
              <button type="button" class="btn btn-sm btn-outline-secondary" id="btnPrint">
                <i class="bi bi-printer"></i> In
              </button>
            </div>
          </div>
        </div>
        
        <!-- Bộ lọc -->
        <div class="row mb-3">
          <div class="col-md-12">
            <div class="card">
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-md-3">
                    <label for="filterPeriod" class="form-label">Khoảng thời gian</label>
                    <select class="form-select" id="filterPeriod">
                      <option value="day">Hôm nay</option>
                      <option value="week">Tuần này</option>
                      <option value="month" selected>Tháng này</option>
                      <option value="quarter">Quý này</option>
                      <option value="year">Năm nay</option>
                      <option value="custom">Tùy chỉnh</option>
                    </select>
                  </div>
                  <div class="col-md-3">
                    <label for="filterDateFrom" class="form-label">Từ ngày</label>
                    <input type="date" class="form-control" id="filterDateFrom">
                  </div>
                  <div class="col-md-3">
                    <label for="filterDateTo" class="form-label">Đến ngày</label>
                    <input type="date" class="form-control" id="filterDateTo">
                  </div>
                  <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" id="btnApplyFilter">
                      <i class="bi bi-funnel"></i> Áp dụng
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Tổng quan -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="card text-white bg-primary">
              <div class="card-body">
                <h5 class="card-title">Tổng doanh thu</h5>
                <h2 class="card-text" id="totalRevenue">0 ₫</h2>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-white bg-success">
              <div class="card-body">
                <h5 class="card-title">Tổng đơn hàng</h5>
                <h2 class="card-text" id="totalOrders">0</h2>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-white bg-info">
              <div class="card-body">
                <h5 class="card-title">Giá trị trung bình</h5>
                <h2 class="card-text" id="averageOrderValue">0 ₫</h2>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-white bg-warning">
              <div class="card-body">
                <h5 class="card-title">Tổng sản phẩm bán</h5>
                <h2 class="card-text" id="totalProductsSold">0</h2>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Biểu đồ doanh thu -->
        <div class="row mb-4">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Biểu đồ doanh thu</h5>
              </div>
              <div class="card-body">
                <canvas id="revenueChart" height="300"></canvas>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Biểu đồ so sánh -->
        <div class="row mb-4">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Doanh thu theo danh mục</h5>
              </div>
              <div class="card-body">
                <canvas id="categoryChart" height="300"></canvas>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Doanh thu theo phương thức thanh toán</h5>
              </div>
              <div class="card-body">
                <canvas id="paymentMethodChart" height="300"></canvas>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Bảng chi tiết -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Chi tiết doanh thu</h5>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped table-hover">
                    <thead>
                      <tr>
                        <th>Ngày</th>
                        <th>Số đơn hàng</th>
                        <th>Doanh thu</th>
                        <th>Sản phẩm bán</th>
                        <th>Giá trị trung bình</th>
                      </tr>
                    </thead>
                    <tbody id="revenueTable">
                      <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                      <tr>
                        <td colspan="5" class="text-center">Đang tải dữ liệu...</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
  
  <!-- Footer -->
  <div id="footer-container"></div>
  
  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <!-- Axios -->
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.3/dist/sweetalert2.all.min.js"></script>
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
  <!-- Common JS -->
  <script src="../../assets/js/common.js"></script>
  <!-- Layout JS -->
  <script src="../../layout/header/header.js"></script>
  <script src="../../layout/sidebar/sidebar.js"></script>
  <!-- Page JS -->
  <script src="sales.js"></script>
</body>
</html>
