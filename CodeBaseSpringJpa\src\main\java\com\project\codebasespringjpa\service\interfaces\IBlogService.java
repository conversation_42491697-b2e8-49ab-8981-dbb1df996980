package com.project.codebasespringjpa.service.interfaces;

import com.project.codebasespringjpa.dto.blog.request.BlogRequest;
import com.project.codebasespringjpa.dto.blog.response.BlogResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface IBlogService {
    Page<BlogResponse> findAll(String title, Pageable pageable);
    BlogResponse findById(Long id);

    BlogResponse create(BlogRequest request);
    BlogResponse update(Long id, BlogRequest request);
    void delete(Long id);
}
