package com.project.codebasespringjpa.service.interfaces;

import com.project.codebasespringjpa.dto.order.request.OrderDetailRequest;
import com.project.codebasespringjpa.dto.order.response.OrderDetailResponse;

import java.util.List;
import java.util.Map;

public interface IOrderDetailService {
    List<OrderDetailResponse> findAll();
    OrderDetailResponse findById(Long id);
    List<OrderDetailResponse> findByOrderId(Long orderId);
    List<OrderDetailResponse> findByProductId(Long productId);
    List<Map<String, Object>> findTopSellingProducts();
    OrderDetailResponse create(Long orderId, OrderDetailRequest request);
    void delete(Long id);
}
